import { Router, Request, Response } from 'express';
import { catchAsync, ValidationError } from '@/middleware/errorHandler';
import { body, query, validationResult } from 'express-validator';
import { RAGService } from '@/services/RAGService';

const router = Router();
const ragService = new RAGService();

// Validation middleware
const validateRequest = (req: Request, _res: Response, next: any) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError(`Validation failed: ${errors.array().map(e => e.msg).join(', ')}`);
  }
  next();
};

// Query knowledge base
router.post('/query', [
  body('question').notEmpty().withMessage('Question is required'),
  body('context').optional().isArray().withMessage('Context must be an array'),
  body('maxResults').optional().isInt({ min: 1, max: 50 }).withMessage('maxResults must be between 1 and 50'),
  body('similarityThreshold').optional().isFloat({ min: 0, max: 1 }).withMessage('similarityThreshold must be between 0 and 1'),
  body('includeMetadata').optional().isBoolean().withMessage('includeMetadata must be a boolean'),
], validateRequest, catchAsync(async (req: Request, res: Response) => {
  const {
    question,
    context = [],
    maxResults = 10,
    similarityThreshold = 0.7,
    includeMetadata = true,
  } = req.body;

  const options = {
    maxResults,
    similarityThreshold,
    includeMetadata,
    context,
  };

  const result = await ragService.queryKnowledgeBase(question, options);

  res.status(200).json({
    success: true,
    data: result,
    timestamp: new Date().toISOString(),
  });
}));

// Create embeddings for text
router.post('/embed', [
  body('text').notEmpty().withMessage('Text is required'),
  body('metadata').optional().isObject().withMessage('Metadata must be an object'),
  body('className').optional().isString().withMessage('className must be a string'),
], validateRequest, catchAsync(async (req: Request, res: Response) => {
  const {
    text,
    metadata = {},
    className = 'Document',
  } = req.body;

  const embedding = await ragService.createEmbedding(text, metadata, className);

  res.status(201).json({
    success: true,
    data: embedding,
    message: 'Embedding created successfully',
    timestamp: new Date().toISOString(),
  });
}));

// Find similar content
router.post('/similar', [
  body('text').optional().withMessage('Either text or vector is required'),
  body('vector').optional().isArray().withMessage('Vector must be an array'),
  body('className').optional().isString().withMessage('className must be a string'),
  body('limit').optional().isInt({ min: 1, max: 100 }).withMessage('limit must be between 1 and 100'),
  body('threshold').optional().isFloat({ min: 0, max: 1 }).withMessage('threshold must be between 0 and 1'),
], validateRequest, catchAsync(async (req: Request, res: Response) => {
  const {
    text,
    vector,
    className = 'Document',
    limit = 10,
    threshold = 0.7,
  } = req.body;

  if (!text && !vector) {
    throw new ValidationError('Either text or vector is required');
  }

  const options = {
    className,
    limit,
    threshold,
  };

  const results = text 
    ? await ragService.findSimilarByText(text, options)
    : await ragService.findSimilarByVector(vector, options);

  res.status(200).json({
    success: true,
    data: results,
    timestamp: new Date().toISOString(),
  });
}));

// Add document to knowledge base
router.post('/documents', [
  body('content').notEmpty().withMessage('Content is required'),
  body('title').optional().isString().withMessage('Title must be a string'),
  body('source').optional().isString().withMessage('Source must be a string'),
  body('metadata').optional().isObject().withMessage('Metadata must be an object'),
  body('className').optional().isString().withMessage('className must be a string'),
], validateRequest, catchAsync(async (req: Request, res: Response) => {
  const {
    content,
    title,
    source,
    metadata = {},
    className = 'Document',
  } = req.body;

  const document = await ragService.addDocument({
    content,
    title,
    source,
    metadata,
    className,
  });

  res.status(201).json({
    success: true,
    data: document,
    message: 'Document added to knowledge base',
    timestamp: new Date().toISOString(),
  });
}));

// Get document by ID
router.get('/documents/:id', catchAsync(async (req: Request, res: Response) => {
  const { id } = req.params;
  const document = await ragService.getDocument(id || '');

  res.status(200).json({
    success: true,
    data: document,
    timestamp: new Date().toISOString(),
  });
}));

// Update document
router.put('/documents/:id', [
  body('content').optional().isString().withMessage('Content must be a string'),
  body('title').optional().isString().withMessage('Title must be a string'),
  body('metadata').optional().isObject().withMessage('Metadata must be an object'),
], validateRequest, catchAsync(async (req: Request, res: Response) => {
  const { id } = req.params;
  const updates = req.body;

  const document = await ragService.updateDocument(id || '', updates);

  res.status(200).json({
    success: true,
    data: document,
    message: 'Document updated successfully',
    timestamp: new Date().toISOString(),
  });
}));

// Delete document
router.delete('/documents/:id', catchAsync(async (req: Request, res: Response) => {
  const { id } = req.params;
  await ragService.deleteDocument(id || '');

  res.status(200).json({
    success: true,
    message: 'Document deleted successfully',
    timestamp: new Date().toISOString(),
  });
}));

// Search documents
router.get('/documents', [
  query('q').optional().isString().withMessage('Query must be a string'),
  query('className').optional().isString().withMessage('className must be a string'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('limit must be between 1 and 100'),
  query('offset').optional().isInt({ min: 0 }).withMessage('offset must be non-negative'),
], validateRequest, catchAsync(async (req: Request, res: Response) => {
  const {
    q: query,
    className,
    limit = 20,
    offset = 0,
  } = req.query;

  const options = {
    className: className as string,
    limit: parseInt(limit as string),
    offset: parseInt(offset as string),
  };

  const results = query 
    ? await ragService.searchDocuments(query as string, options)
    : await ragService.listDocuments(options);

  res.status(200).json({
    success: true,
    data: results,
    timestamp: new Date().toISOString(),
  });
}));

// Generate contextual answer
router.post('/answer', [
  body('question').notEmpty().withMessage('Question is required'),
  body('context').optional().isArray().withMessage('Context must be an array'),
  body('useGraph').optional().isBoolean().withMessage('useGraph must be a boolean'),
  body('maxContextLength').optional().isInt({ min: 100, max: 10000 }).withMessage('maxContextLength must be between 100 and 10000'),
], validateRequest, catchAsync(async (req: Request, res: Response) => {
  const {
    question,
    context = [],
    useGraph = true,
    maxContextLength = 4000,
  } = req.body;

  const options = {
    context,
    useGraph,
    maxContextLength,
  };

  const answer = await ragService.generateAnswer(question, options);

  res.status(200).json({
    success: true,
    data: answer,
    timestamp: new Date().toISOString(),
  });
}));

// Get RAG statistics
router.get('/stats', catchAsync(async (_req: Request, res: Response) => {
  const stats = await ragService.getStats();

  res.status(200).json({
    success: true,
    data: stats,
    timestamp: new Date().toISOString(),
  });
}));

// Reindex documents
router.post('/reindex', [
  body('className').optional().isString().withMessage('className must be a string'),
  body('batchSize').optional().isInt({ min: 1, max: 1000 }).withMessage('batchSize must be between 1 and 1000'),
], validateRequest, catchAsync(async (req: Request, res: Response) => {
  const {
    className,
    batchSize = 100,
  } = req.body;

  const result = await ragService.reindexDocuments({
    className,
    batchSize,
  });

  res.status(200).json({
    success: true,
    data: result,
    message: 'Reindexing completed successfully',
    timestamp: new Date().toISOString(),
  });
}));

export { router as ragRoutes };
