import { Router, Request, Response } from 'express';
import { catchAsync, ValidationError } from '@/middleware/errorHandler';
import { body, query, validationResult } from 'express-validator';
import { GraphService } from '@/services/GraphService';

const router = Router();
const graphService = new GraphService();

// Validation middleware
const validateRequest = (req: Request, _res: Response, next: any) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError(`Validation failed: ${errors.array().map(e => e.msg).join(', ')}`);
  }
  next();
};

// Get graph data with optional filtering
router.get('/', [
  query('nodeTypes').optional().isString().withMessage('nodeTypes must be a string'),
  query('relationshipTypes').optional().isString().withMessage('relationshipTypes must be a string'),
  query('limit').optional().isInt({ min: 1, max: 10000 }).withMessage('limit must be between 1 and 10000'),
  query('search').optional().isString().withMessage('search must be a string'),
], validateRequest, catchAsync(async (req: Request, res: Response) => {
  const {
    nodeTypes,
    relationshipTypes,
    limit = 1000,
    search,
  } = req.query;

  const filters: any = {
    limit: parseInt(limit as string),
  };

  if (nodeTypes) {
    filters.nodeTypes = (nodeTypes as string).split(',');
  }

  if (relationshipTypes) {
    filters.relationshipTypes = (relationshipTypes as string).split(',');
  }

  if (search) {
    filters.search = search as string;
  }

  const graphData = await graphService.getGraphData(filters);

  res.status(200).json({
    success: true,
    data: graphData,
    timestamp: new Date().toISOString(),
  });
}));

// Get specific node by ID
router.get('/nodes/:id', catchAsync(async (req: Request, res: Response) => {
  const { id } = req.params;
  const node = await graphService.getNodeById(id || '');

  res.status(200).json({
    success: true,
    data: node,
    timestamp: new Date().toISOString(),
  });
}));

// Get node relationships
router.get('/nodes/:id/relationships', [
  query('direction').optional().isIn(['incoming', 'outgoing', 'both']).withMessage('direction must be incoming, outgoing, or both'),
  query('types').optional().isString().withMessage('types must be a string'),
  query('limit').optional().isInt({ min: 1, max: 1000 }).withMessage('limit must be between 1 and 1000'),
], validateRequest, catchAsync(async (req: Request, res: Response) => {
  const { id } = req.params;
  const {
    direction = 'both',
    types,
    limit = 100,
  } = req.query;

  const filters: any = {
    direction: direction as 'incoming' | 'outgoing' | 'both',
    limit: parseInt(limit as string),
  };

  if (types) {
    filters.types = (types as string).split(',');
  }

  const relationships = await graphService.getNodeRelationships(id || '', filters);

  res.status(200).json({
    success: true,
    data: relationships,
    timestamp: new Date().toISOString(),
  });
}));

// Search nodes
router.get('/search', [
  query('q').notEmpty().withMessage('Search query is required'),
  query('nodeTypes').optional().isString().withMessage('nodeTypes must be a string'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('limit must be between 1 and 100'),
], validateRequest, catchAsync(async (req: Request, res: Response) => {
  const {
    q: query,
    nodeTypes,
    limit = 20,
  } = req.query;

  const filters: any = {
    limit: parseInt(limit as string),
  };

  if (nodeTypes) {
    filters.nodeTypes = (nodeTypes as string).split(',');
  }

  const results = await graphService.searchNodes(query as string, filters);

  res.status(200).json({
    success: true,
    data: results,
    timestamp: new Date().toISOString(),
  });
}));

// Create new node
router.post('/nodes', [
  body('type').notEmpty().withMessage('Node type is required'),
  body('properties').isObject().withMessage('Properties must be an object'),
  body('properties.name').notEmpty().withMessage('Node name is required'),
], validateRequest, catchAsync(async (req: Request, res: Response) => {
  const { type, properties } = req.body;

  const node = await graphService.createNode(type, properties);

  res.status(201).json({
    success: true,
    data: node,
    message: 'Node created successfully',
    timestamp: new Date().toISOString(),
  });
}));

// Update node
router.put('/nodes/:id', [
  body('properties').isObject().withMessage('Properties must be an object'),
], validateRequest, catchAsync(async (req: Request, res: Response) => {
  const { id } = req.params;
  const { properties } = req.body;

  const node = await graphService.updateNode(id || '', properties);

  res.status(200).json({
    success: true,
    data: node,
    message: 'Node updated successfully',
    timestamp: new Date().toISOString(),
  });
}));

// Delete node
router.delete('/nodes/:id', catchAsync(async (req: Request, res: Response) => {
  const { id } = req.params;

  await graphService.deleteNode(id || '');

  res.status(200).json({
    success: true,
    message: 'Node deleted successfully',
    timestamp: new Date().toISOString(),
  });
}));

// Create relationship
router.post('/relationships', [
  body('fromId').notEmpty().withMessage('From node ID is required'),
  body('toId').notEmpty().withMessage('To node ID is required'),
  body('type').notEmpty().withMessage('Relationship type is required'),
  body('properties').optional().isObject().withMessage('Properties must be an object'),
], validateRequest, catchAsync(async (req: Request, res: Response) => {
  const { fromId, toId, type, properties = {} } = req.body;

  const relationship = await graphService.createRelationship(fromId, toId, type, properties);

  res.status(201).json({
    success: true,
    data: relationship,
    message: 'Relationship created successfully',
    timestamp: new Date().toISOString(),
  });
}));

// Extract knowledge from text
router.post('/extract', [
  body('text').notEmpty().withMessage('Text is required'),
  body('source').optional().isString().withMessage('Source must be a string'),
  body('extractionType').optional().isIn(['supplement', 'ingredient', 'study', 'general']).withMessage('Invalid extraction type'),
], validateRequest, catchAsync(async (req: Request, res: Response) => {
  const { text, source, extractionType = 'general' } = req.body;

  const extractedData = await graphService.extractKnowledgeFromText(text, {
    source,
    extractionType,
  });

  res.status(200).json({
    success: true,
    data: extractedData,
    message: 'Knowledge extracted successfully',
    timestamp: new Date().toISOString(),
  });
}));

// Expand graph with new connections
router.post('/expand', [
  body('nodeId').notEmpty().withMessage('Node ID is required'),
  body('expansionType').optional().isIn(['related', 'similar', 'interactions', 'studies']).withMessage('Invalid expansion type'),
  body('limit').optional().isInt({ min: 1, max: 100 }).withMessage('limit must be between 1 and 100'),
], validateRequest, catchAsync(async (req: Request, res: Response) => {
  const { nodeId, expansionType = 'related', limit = 10 } = req.body;

  const expandedData = await graphService.expandGraph(nodeId, {
    expansionType,
    limit,
  });

  res.status(200).json({
    success: true,
    data: expandedData,
    message: 'Graph expanded successfully',
    timestamp: new Date().toISOString(),
  });
}));

// Get graph statistics
router.get('/stats', catchAsync(async (_req: Request, res: Response) => {
  const stats = await graphService.getGraphStats();

  res.status(200).json({
    success: true,
    data: stats,
    timestamp: new Date().toISOString(),
  });
}));

// Clean up graph data
router.delete('/cleanup', [
  query('dryRun').optional().isBoolean().withMessage('dryRun must be a boolean'),
], validateRequest, catchAsync(async (req: Request, res: Response) => {
  const { dryRun = true } = req.query;

  const cleanupResults = await graphService.cleanupGraph(dryRun === 'true');

  res.status(200).json({
    success: true,
    data: cleanupResults,
    message: dryRun === 'true' ? 'Cleanup analysis completed' : 'Graph cleanup completed',
    timestamp: new Date().toISOString(),
  });
}));

export { router as graphRoutes };
