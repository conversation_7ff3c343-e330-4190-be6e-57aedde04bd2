import { Router, Request, Response } from 'express';
import { catchAsync } from '@/middleware/errorHandler';
import { neo4jHealthCheck, getNeo4jStats } from '@/config/neo4j';
import { weaviateHealthCheck, getWeaviateStats } from '@/config/weaviate';
import { redisHealthCheck, getRedisStats } from '@/config/redis';
import { mongoDBHealthCheck, getMongoDBStats } from '@/config/mongodb';
import { config } from '@/config/environment';

const router = Router();

// Basic health check
router.get('/', catchAsync(async (_req: Request, res: Response) => {
  res.status(200).json({
    success: true,
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: config.nodeEnv,
    version: '1.0.0',
  });
}));

// Detailed health check with database status
router.get('/detailed', catchAsync(async (_req: Request, res: Response) => {
  const healthChecks = await Promise.allSettled([
    neo4jHealthCheck(),
    weaviateHealthCheck(),
    redisHealthCheck(),
    mongoDBHealthCheck(),
  ]);

  const [neo4jHealth, weaviateHealth, redisHealth, mongoDBHealth] = healthChecks;

  const services = {
    neo4j: {
      status: neo4jHealth.status === 'fulfilled' && neo4jHealth.value ? 'healthy' : 'unhealthy',
      error: neo4jHealth.status === 'rejected' ? neo4jHealth.reason?.message : null,
    },
    weaviate: {
      status: weaviateHealth.status === 'fulfilled' && weaviateHealth.value ? 'healthy' : 'unhealthy',
      error: weaviateHealth.status === 'rejected' ? weaviateHealth.reason?.message : null,
    },
    redis: {
      status: redisHealth.status === 'fulfilled' && redisHealth.value ? 'healthy' : 'unhealthy',
      error: redisHealth.status === 'rejected' ? redisHealth.reason?.message : null,
    },
    mongodb: {
      status: mongoDBHealth.status === 'fulfilled' && mongoDBHealth.value ? 'healthy' : 'unhealthy',
      error: mongoDBHealth.status === 'rejected' ? mongoDBHealth.reason?.message : null,
    },
  };

  const allHealthy = Object.values(services).every(service => service.status === 'healthy');

  res.status(allHealthy ? 200 : 503).json({
    success: allHealthy,
    status: allHealthy ? 'OK' : 'DEGRADED',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: config.nodeEnv,
    version: '1.0.0',
    services,
  });
}));

// Database statistics
router.get('/stats', catchAsync(async (_req: Request, res: Response) => {
  const statsPromises = await Promise.allSettled([
    getNeo4jStats(),
    getWeaviateStats(),
    getRedisStats(),
    getMongoDBStats(),
  ]);

  const [neo4jStats, weaviateStats, redisStats, mongoDBStats] = statsPromises;

  const stats = {
    neo4j: neo4jStats.status === 'fulfilled' ? neo4jStats.value : { error: neo4jStats.reason?.message },
    weaviate: weaviateStats.status === 'fulfilled' ? weaviateStats.value : { error: weaviateStats.reason?.message },
    redis: redisStats.status === 'fulfilled' ? redisStats.value : { error: redisStats.reason?.message },
    mongodb: mongoDBStats.status === 'fulfilled' ? mongoDBStats.value : { error: mongoDBStats.reason?.message },
  };

  res.status(200).json({
    success: true,
    timestamp: new Date().toISOString(),
    stats,
  });
}));

// System information
router.get('/system', catchAsync(async (_req: Request, res: Response) => {
  const memoryUsage = process.memoryUsage();
  const cpuUsage = process.cpuUsage();

  res.status(200).json({
    success: true,
    timestamp: new Date().toISOString(),
    system: {
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch,
      uptime: process.uptime(),
      memory: {
        rss: Math.round(memoryUsage.rss / 1024 / 1024), // MB
        heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024), // MB
        heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024), // MB
        external: Math.round(memoryUsage.external / 1024 / 1024), // MB
      },
      cpu: {
        user: cpuUsage.user,
        system: cpuUsage.system,
      },
    },
  });
}));

export { router as healthRoutes };
