import { executeNeo4jQ<PERSON>y, executeNeo4jTransaction } from '@/config/neo4j';
import { cacheGet, cacheSet, cacheDel } from '@/config/redis';
import { AIService } from '@/services/AIService';
import { config } from '@/config/environment';
import { logger, logGraphOperation, logError } from '@/utils/logger';
import { NotFoundError, DatabaseError } from '@/middleware/errorHandler';
import { v4 as uuidv4 } from 'uuid';

interface GraphFilters {
  nodeTypes?: string[];
  relationshipTypes?: string[];
  limit?: number;
  search?: string;
}

interface RelationshipFilters {
  direction?: 'incoming' | 'outgoing' | 'both';
  types?: string[];
  limit?: number;
}

interface SearchFilters {
  nodeTypes?: string[];
  limit?: number;
}

interface ExtractionOptions {
  source?: string;
  extractionType?: 'supplement' | 'ingredient' | 'study' | 'general';
}

interface ExpansionOptions {
  expansionType?: 'related' | 'similar' | 'interactions' | 'studies';
  limit?: number;
}

export class GraphService {
  private aiService: AIService;

  constructor() {
    this.aiService = new AIService();
  }

  async getGraphData(filters: GraphFilters): Promise<any> {
    const startTime = Date.now();
    const cacheKey = `graph:data:${JSON.stringify(filters)}`;

    try {
      // Try to get from cache first
      const cached = await cacheGet(cacheKey);
      if (cached) {
        logGraphOperation('Get graph data (cached)', cached.nodes?.length, cached.relationships?.length);
        return cached;
      }

      // Build query based on filters
      let nodeQuery = 'MATCH (n)';
      let relationshipQuery = 'MATCH (a)-[r]->(b)';
      const params: any = {};

      // Add node type filters
      if (filters.nodeTypes && filters.nodeTypes.length > 0) {
        const labels = filters.nodeTypes.map((type) => `n:${type}`).join(' OR ');
        nodeQuery += ` WHERE ${labels}`;
      }

      // Add search filter
      if (filters.search) {
        const searchCondition = filters.nodeTypes && filters.nodeTypes.length > 0 ? ' AND ' : ' WHERE ';
        nodeQuery += `${searchCondition}(n.name CONTAINS $search OR n.description CONTAINS $search)`;
        params.search = filters.search;
      }

      // Add relationship type filters
      if (filters.relationshipTypes && filters.relationshipTypes.length > 0) {
        const types = filters.relationshipTypes.map(type => `'${type}'`).join(', ');
        relationshipQuery += ` WHERE type(r) IN [${types}]`;
      }

      // Add limits
      const limit = Math.min(filters.limit || 1000, config.graph.maxNodes);
      nodeQuery += ` RETURN n LIMIT ${limit}`;
      relationshipQuery += ` RETURN a, r, b LIMIT ${Math.min(limit * 5, config.graph.maxRelationships)}`;

      // Execute queries
      const [nodeResult, relationshipResult] = await Promise.all([
        executeNeo4jQuery(nodeQuery, params),
        executeNeo4jQuery(relationshipQuery, params),
      ]);

      // Process results
      const nodes = nodeResult.records.map((record: any) => {
        const node = record.get('n');
        return {
          id: node.identity.toString(),
          labels: node.labels,
          properties: node.properties,
        };
      });

      const relationships = relationshipResult.records.map((record: any) => {
        const source = record.get('a');
        const relationship = record.get('r');
        const target = record.get('b');

        return {
          id: relationship.identity.toString(),
          type: relationship.type,
          properties: relationship.properties,
          source: source.identity.toString(),
          target: target.identity.toString(),
        };
      });

      const result = { nodes, relationships };

      // Cache the result
      await cacheSet(cacheKey, result, config.cache.ttl);

      const duration = Date.now() - startTime;
      logGraphOperation('Get graph data', nodes.length, relationships.length, duration);

      return result;
    } catch (error) {
      logError('Failed to get graph data', error, { filters });
      throw new DatabaseError('Failed to retrieve graph data');
    }
  }

  async getNodeById(id: string): Promise<any> {
    const startTime = Date.now();
    const cacheKey = `node:${id}`;

    try {
      // Try cache first
      const cached = await cacheGet(cacheKey);
      if (cached) {
        return cached;
      }

      const query = 'MATCH (n) WHERE id(n) = $id RETURN n';
      const result = await executeNeo4jQuery(query, { id: parseInt(id) });

      if (result.records.length === 0) {
        throw new NotFoundError(`Node with id ${id} not found`);
      }

      const node = result.records[0].get('n');
      const nodeData = {
        id: node.identity.toString(),
        labels: node.labels,
        properties: node.properties,
      };

      // Cache the result
      await cacheSet(cacheKey, nodeData, config.cache.ttl);

      const duration = Date.now() - startTime;
      logGraphOperation('Get node by ID', 1, 0, duration);

      return nodeData;
    } catch (error) {
      if (error instanceof NotFoundError) throw error;
      logError('Failed to get node by ID', error, { id });
      throw new DatabaseError('Failed to retrieve node');
    }
  }

  async getNodeRelationships(id: string, filters: RelationshipFilters): Promise<any> {
    const startTime = Date.now();
    const cacheKey = `node:${id}:relationships:${JSON.stringify(filters)}`;

    try {
      // Try cache first
      const cached = await cacheGet(cacheKey);
      if (cached) {
        return cached;
      }

      let query: string;
      const params: any = { id: parseInt(id) };

      // Build query based on direction
      switch (filters.direction) {
        case 'incoming':
          query = 'MATCH (a)-[r]->(n) WHERE id(n) = $id';
          break;
        case 'outgoing':
          query = 'MATCH (n)-[r]->(b) WHERE id(n) = $id';
          break;
        default:
          query = 'MATCH (n)-[r]-(other) WHERE id(n) = $id';
      }

      // Add relationship type filters
      if (filters.types && filters.types.length > 0) {
        const types = filters.types.map(type => `'${type}'`).join(', ');
        query += ` AND type(r) IN [${types}]`;
      }

      // Add return clause and limit
      const limit = Math.min(filters.limit || 100, 1000);
      if (filters.direction === 'incoming') {
        query += ` RETURN a as node, r, n as target LIMIT ${limit}`;
      } else if (filters.direction === 'outgoing') {
        query += ` RETURN n as source, r, b as node LIMIT ${limit}`;
      } else {
        query += ` RETURN n as center, r, other as node LIMIT ${limit}`;
      }

      const result = await executeNeo4jQuery(query, params);

      const relationships = result.records.map((record: any) => {
        const relationship = record.get('r');
        const otherNode = record.get('node') || record.get('target') || record.get('source');

        return {
          id: relationship.identity.toString(),
          type: relationship.type,
          properties: relationship.properties,
          direction: filters.direction,
          relatedNode: {
            id: otherNode.identity.toString(),
            labels: otherNode.labels,
            properties: otherNode.properties,
          },
        };
      });

      // Cache the result
      await cacheSet(cacheKey, relationships, config.cache.ttl);

      const duration = Date.now() - startTime;
      logGraphOperation('Get node relationships', 0, relationships.length, duration);

      return relationships;
    } catch (error) {
      logError('Failed to get node relationships', error, { id, filters });
      throw new DatabaseError('Failed to retrieve node relationships');
    }
  }

  async searchNodes(searchQuery: string, filters: SearchFilters): Promise<any> {
    const startTime = Date.now();
    const cacheKey = `search:${searchQuery}:${JSON.stringify(filters)}`;

    try {
      // Try cache first
      const cached = await cacheGet(cacheKey);
      if (cached) {
        return cached;
      }

      // Use full-text search if available, otherwise use CONTAINS
      let query = `
        CALL db.index.fulltext.queryNodes('supplement_search', $searchQuery) 
        YIELD node, score
        RETURN node, score
        UNION
        CALL db.index.fulltext.queryNodes('ingredient_search', $searchQuery) 
        YIELD node, score
        RETURN node, score
        UNION
        CALL db.index.fulltext.queryNodes('effect_search', $searchQuery) 
        YIELD node, score
        RETURN node, score
        UNION
        CALL db.index.fulltext.queryNodes('study_search', $searchQuery) 
        YIELD node, score
        RETURN node, score
        ORDER BY score DESC
        LIMIT $limit
      `;

      const limit = Math.min(filters.limit || 20, 100);
      const params = { 
        searchQuery: `*${searchQuery}*`,
        limit 
      };

      let result;
      try {
        result = await executeNeo4jQuery(query, params);
      } catch (error) {
        // Fallback to simple text search if full-text search fails
        query = `
          MATCH (n) 
          WHERE n.name CONTAINS $search 
             OR n.description CONTAINS $search
             OR n.title CONTAINS $search
        `;

        if (filters.nodeTypes && filters.nodeTypes.length > 0) {
          const labels = filters.nodeTypes.map(type => `n:${type}`).join(' OR ');
          query += ` AND (${labels})`;
        }

        query += ` RETURN n, 1.0 as score ORDER BY n.name LIMIT $limit`;
        
        result = await executeNeo4jQuery(query, { 
          search: searchQuery,
          limit 
        });
      }

      const nodes = result.records.map((record: any) => {
        const node = record.get('node') || record.get('n');
        const score = record.get('score');

        return {
          id: node.identity.toString(),
          labels: node.labels,
          properties: node.properties,
          score: typeof score === 'number' ? score : score?.toNumber() || 1.0,
        };
      });

      // Cache the result
      await cacheSet(cacheKey, nodes, config.cache.ttl);

      const duration = Date.now() - startTime;
      logGraphOperation('Search nodes', nodes.length, 0, duration);

      return nodes;
    } catch (error) {
      logError('Failed to search nodes', error, { searchQuery, filters });
      throw new DatabaseError('Failed to search nodes');
    }
  }

  async createNode(type: string, properties: any): Promise<any> {
    const startTime = Date.now();

    try {
      // Generate ID if not provided
      if (!properties.id) {
        properties.id = uuidv4();
      }

      // Add timestamps
      properties.createdAt = new Date().toISOString();
      properties.updatedAt = new Date().toISOString();

      const query = `
        CREATE (n:${type} $properties)
        RETURN n
      `;

      const result = await executeNeo4jQuery(query, { properties });
      const node = result.records[0].get('n');

      const nodeData = {
        id: node.identity.toString(),
        labels: node.labels,
        properties: node.properties,
      };

      // Invalidate related caches
      await this.invalidateGraphCaches();

      const duration = Date.now() - startTime;
      logGraphOperation('Create node', 1, 0, duration);

      return nodeData;
    } catch (error) {
      logError('Failed to create node', error, { type, properties });
      throw new DatabaseError('Failed to create node');
    }
  }

  async updateNode(id: string, properties: any): Promise<any> {
    const startTime = Date.now();

    try {
      // Add update timestamp
      properties.updatedAt = new Date().toISOString();

      const query = `
        MATCH (n) WHERE id(n) = $id
        SET n += $properties
        RETURN n
      `;

      const result = await executeNeo4jQuery(query, { 
        id: parseInt(id), 
        properties 
      });

      if (result.records.length === 0) {
        throw new NotFoundError(`Node with id ${id} not found`);
      }

      const node = result.records[0].get('n');
      const nodeData = {
        id: node.identity.toString(),
        labels: node.labels,
        properties: node.properties,
      };

      // Invalidate caches
      await cacheDel(`node:${id}`);
      await this.invalidateGraphCaches();

      const duration = Date.now() - startTime;
      logGraphOperation('Update node', 1, 0, duration);

      return nodeData;
    } catch (error) {
      if (error instanceof NotFoundError) throw error;
      logError('Failed to update node', error, { id, properties });
      throw new DatabaseError('Failed to update node');
    }
  }

  async deleteNode(id: string): Promise<void> {
    const startTime = Date.now();

    try {
      const query = `
        MATCH (n) WHERE id(n) = $id
        DETACH DELETE n
        RETURN count(n) as deleted
      `;

      const result = await executeNeo4jQuery(query, { id: parseInt(id) });
      const deleted = result.records[0].get('deleted').toNumber();

      if (deleted === 0) {
        throw new NotFoundError(`Node with id ${id} not found`);
      }

      // Invalidate caches
      await cacheDel(`node:${id}`);
      await this.invalidateGraphCaches();

      const duration = Date.now() - startTime;
      logGraphOperation('Delete node', 1, 0, duration);
    } catch (error) {
      if (error instanceof NotFoundError) throw error;
      logError('Failed to delete node', error, { id });
      throw new DatabaseError('Failed to delete node');
    }
  }

  async createRelationship(fromId: string, toId: string, type: string, properties: any = {}): Promise<any> {
    const startTime = Date.now();

    try {
      // Add timestamps
      properties.createdAt = new Date().toISOString();
      properties.updatedAt = new Date().toISOString();

      const query = `
        MATCH (a), (b)
        WHERE id(a) = $fromId AND id(b) = $toId
        CREATE (a)-[r:${type} $properties]->(b)
        RETURN r, a, b
      `;

      const result = await executeNeo4jQuery(query, {
        fromId: parseInt(fromId),
        toId: parseInt(toId),
        properties,
      });

      if (result.records.length === 0) {
        throw new NotFoundError('One or both nodes not found');
      }

      const relationship = result.records[0].get('r');
      const sourceNode = result.records[0].get('a');
      const targetNode = result.records[0].get('b');

      const relationshipData = {
        id: relationship.identity.toString(),
        type: relationship.type,
        properties: relationship.properties,
        source: {
          id: sourceNode.identity.toString(),
          labels: sourceNode.labels,
          properties: sourceNode.properties,
        },
        target: {
          id: targetNode.identity.toString(),
          labels: targetNode.labels,
          properties: targetNode.properties,
        },
      };

      // Invalidate related caches
      await this.invalidateGraphCaches();

      const duration = Date.now() - startTime;
      logGraphOperation('Create relationship', 0, 1, duration);

      return relationshipData;
    } catch (error) {
      if (error instanceof NotFoundError) throw error;
      logError('Failed to create relationship', error, { fromId, toId, type, properties });
      throw new DatabaseError('Failed to create relationship');
    }
  }

  async extractKnowledgeFromText(text: string, options: ExtractionOptions): Promise<any> {
    const startTime = Date.now();

    try {
      // Use AI service to extract entities and relationships
      const extraction = await this.aiService.extractEntitiesAndRelationships(text, {
        entityTypes: this.getEntityTypesForExtraction(options.extractionType),
        confidence: 0.7,
      });

      // Create nodes and relationships in the graph
      const createdNodes = [];
      const createdRelationships = [];

      // Create nodes for extracted entities
      for (const entity of extraction.entities) {
        try {
          const node = await this.createNode(entity.type, {
            name: entity.name,
            description: entity.description,
            confidence: entity.confidence,
            source: options.source,
            extractedFrom: text.substring(0, 200) + '...',
          });
          createdNodes.push(node);
        } catch (error) {
          logger.warn(`Failed to create node for entity: ${entity.name}`, error);
        }
      }

      // Create relationships
      for (const relationship of extraction.relationships) {
        try {
          // Find nodes by name (simplified approach)
          const sourceNode = createdNodes.find(n => 
            n.properties.name === relationship.source
          );
          const targetNode = createdNodes.find(n => 
            n.properties.name === relationship.target
          );

          if (sourceNode && targetNode) {
            const rel = await this.createRelationship(
              sourceNode.id,
              targetNode.id,
              relationship.type,
              {
                confidence: relationship.confidence,
                source: options.source,
              }
            );
            createdRelationships.push(rel);
          }
        } catch (error) {
          logger.warn(`Failed to create relationship: ${relationship.type}`, error);
        }
      }

      const duration = Date.now() - startTime;
      logGraphOperation('Extract knowledge', createdNodes.length, createdRelationships.length, duration);

      return {
        extractedEntities: extraction.entities.length,
        extractedRelationships: extraction.relationships.length,
        createdNodes: createdNodes.length,
        createdRelationships: createdRelationships.length,
        nodes: createdNodes,
        relationships: createdRelationships,
      };
    } catch (error) {
      logError('Failed to extract knowledge from text', error, { options });
      throw new DatabaseError('Failed to extract knowledge from text');
    }
  }

  async expandGraph(nodeId: string, options: ExpansionOptions): Promise<any> {
    const startTime = Date.now();

    try {
      // Get the node to expand
      const node = await this.getNodeById(nodeId);
      
      // Use AI to find related concepts
      const relatedConcepts = await this.aiService.findRelatedConcepts(
        node.properties.name,
        {
          type: options.expansionType,
          limit: options.limit,
        }
      );

      const expandedNodes = [];
      const expandedRelationships = [];

      // Create nodes for related concepts
      for (const concept of relatedConcepts) {
        try {
          // Check if node already exists
          const existingNodes = await this.searchNodes(concept.name, { limit: 1 });
          
          let relatedNode;
          if (existingNodes.length > 0) {
            relatedNode = existingNodes[0];
          } else {
            // Create new node
            relatedNode = await this.createNode(concept.type, {
              name: concept.name,
              description: concept.description,
              confidence: concept.confidence,
              source: 'AI_EXPANSION',
            });
            expandedNodes.push(relatedNode);
          }

          // Create relationship
          const relationship = await this.createRelationship(
            nodeId,
            relatedNode.id,
            concept.relationshipType || 'RELATED_TO',
            {
              confidence: concept.confidence,
              source: 'AI_EXPANSION',
              expansionType: options.expansionType,
            }
          );
          expandedRelationships.push(relationship);
        } catch (error) {
          logger.warn(`Failed to expand with concept: ${concept.name}`, error);
        }
      }

      const duration = Date.now() - startTime;
      logGraphOperation('Expand graph', expandedNodes.length, expandedRelationships.length, duration);

      return {
        sourceNode: node,
        expandedNodes,
        expandedRelationships,
        expansionType: options.expansionType,
      };
    } catch (error) {
      logError('Failed to expand graph', error, { nodeId, options });
      throw new DatabaseError('Failed to expand graph');
    }
  }

  async getGraphStats(): Promise<any> {
    const startTime = Date.now();
    const cacheKey = 'graph:stats';

    try {
      // Try cache first
      const cached = await cacheGet(cacheKey);
      if (cached) {
        return cached;
      }

      const queries = [
        'MATCH (n) RETURN labels(n) as label, count(n) as count',
        'MATCH ()-[r]->() RETURN type(r) as type, count(r) as count',
        'MATCH (n) RETURN count(n) as totalNodes',
        'MATCH ()-[r]->() RETURN count(r) as totalRelationships',
      ];

      const results = await Promise.all(
        queries.map(query => executeNeo4jQuery(query))
      );

      const nodeStats = results[0].records.map((record: any) => ({
        label: record.get('label')[0] || 'Unknown',
        count: record.get('count').toNumber(),
      }));

      const relationshipStats = results[1].records.map((record: any) => ({
        type: record.get('type'),
        count: record.get('count').toNumber(),
      }));

      const totalNodes = results[2].records[0]?.get('totalNodes').toNumber() || 0;
      const totalRelationships = results[3].records[0]?.get('totalRelationships').toNumber() || 0;

      const stats = {
        totalNodes,
        totalRelationships,
        nodesByType: nodeStats,
        relationshipsByType: relationshipStats,
        lastUpdated: new Date().toISOString(),
      };

      // Cache for 5 minutes
      await cacheSet(cacheKey, stats, 300);

      const duration = Date.now() - startTime;
      logGraphOperation('Get graph stats', totalNodes, totalRelationships, duration);

      return stats;
    } catch (error) {
      logError('Failed to get graph stats', error);
      throw new DatabaseError('Failed to get graph statistics');
    }
  }

  async cleanupGraph(dryRun: boolean = true): Promise<any> {
    const startTime = Date.now();

    try {
      const cleanupQueries = [
        // Find orphaned nodes (nodes with no relationships)
        'MATCH (n) WHERE NOT (n)--() RETURN n.id as id, labels(n) as labels, n.name as name',
        
        // Find duplicate nodes (same name and type)
        `MATCH (n) 
         WITH n.name as name, labels(n) as labels, collect(n) as nodes
         WHERE size(nodes) > 1
         RETURN name, labels, nodes`,
        
        // Find low-confidence relationships
        'MATCH ()-[r]->() WHERE r.confidence < 0.5 RETURN id(r) as id, type(r) as type, r.confidence as confidence',
      ];

      const results = await Promise.all(
        cleanupQueries.map(query => executeNeo4jQuery(query))
      );

      const orphanedNodes = results[0].records.map((record: any) => ({
        id: record.get('id'),
        labels: record.get('labels'),
        name: record.get('name'),
      }));

      const duplicateGroups = results[1].records.map((record: any) => ({
        name: record.get('name'),
        labels: record.get('labels'),
        nodes: record.get('nodes'),
      }));

      const lowConfidenceRelationships = results[2].records.map((record: any) => ({
        id: record.get('id'),
        type: record.get('type'),
        confidence: record.get('confidence'),
      }));

      const cleanupPlan = {
        orphanedNodes: orphanedNodes.length,
        duplicateGroups: duplicateGroups.length,
        lowConfidenceRelationships: lowConfidenceRelationships.length,
        details: {
          orphanedNodes,
          duplicateGroups,
          lowConfidenceRelationships,
        },
      };

      if (!dryRun) {
        // Execute cleanup operations
        const cleanupOperations = [];

        // Remove orphaned nodes
        if (orphanedNodes.length > 0) {
          cleanupOperations.push({
            query: 'MATCH (n) WHERE NOT (n)--() DELETE n',
            parameters: {},
          });
        }

        // Remove low-confidence relationships
        if (lowConfidenceRelationships.length > 0) {
          cleanupOperations.push({
            query: 'MATCH ()-[r]->() WHERE r.confidence < 0.5 DELETE r',
            parameters: {},
          });
        }

        if (cleanupOperations.length > 0) {
          await executeNeo4jTransaction(cleanupOperations);
          await this.invalidateGraphCaches();
        }
      }

      const duration = Date.now() - startTime;
      logGraphOperation('Cleanup graph', 0, 0, duration);

      return {
        dryRun,
        cleanupPlan,
        executed: !dryRun,
      };
    } catch (error) {
      logError('Failed to cleanup graph', error, { dryRun });
      throw new DatabaseError('Failed to cleanup graph');
    }
  }

  /**
   * Update supplement knowledge in the graph
   */
  async updateSupplementKnowledge(
    supplementName: string,
    analysisData: any,
    researchData: any
  ): Promise<any> {
    const startTime = Date.now();

    try {
      // Find or create supplement node
      let supplementNode;
      const existingNodes = await this.searchNodes(supplementName, {
        nodeTypes: ['Supplement'],
        limit: 1
      });

      if (existingNodes.length > 0) {
        // Update existing node
        supplementNode = await this.updateNode(existingNodes[0].id, {
          name: supplementName,
          lastAnalyzed: new Date().toISOString(),
          confidence: analysisData.confidence || 0.5,
          analysisVersion: '1.0'
        });
      } else {
        // Create new supplement node
        supplementNode = await this.createNode('Supplement', {
          name: supplementName,
          description: `Supplement: ${supplementName}`,
          lastAnalyzed: new Date().toISOString(),
          confidence: analysisData.confidence || 0.5,
          analysisVersion: '1.0'
        });
      }

      const createdNodes = [];
      const createdRelationships = [];

      // Create health domain nodes and relationships
      if (analysisData.healthDomains) {
        for (const domain of analysisData.healthDomains) {
          try {
            // Find or create health domain node
            let domainNode;
            const existingDomainNodes = await this.searchNodes(domain.name, {
              nodeTypes: ['HealthDomain'],
              limit: 1
            });

            if (existingDomainNodes.length > 0) {
              domainNode = existingDomainNodes[0];
            } else {
              domainNode = await this.createNode('HealthDomain', {
                name: domain.name,
                type: domain.type,
                description: domain.description
              });
              createdNodes.push(domainNode);
            }

            // Create relationship
            const relationship = await this.createRelationship(
              supplementNode.id,
              domainNode.id,
              'AFFECTS',
              {
                confidence: domain.confidence,
                source: 'AI_ANALYSIS',
                relatedProperties: domain.relatedProperties
              }
            );
            createdRelationships.push(relationship);
          } catch (error) {
            logger.warn(`Failed to process health domain: ${domain.name}`, error);
          }
        }
      }

      // Create property nodes and relationships
      if (analysisData.properties) {
        for (const property of analysisData.properties) {
          try {
            // Find or create property node
            let propertyNode;
            const existingPropertyNodes = await this.searchNodes(property.name, {
              nodeTypes: ['Property'],
              limit: 1
            });

            if (existingPropertyNodes.length > 0) {
              propertyNode = existingPropertyNodes[0];
            } else {
              propertyNode = await this.createNode('Property', {
                name: property.name,
                category: property.category,
                description: `Property: ${property.name}`
              });
              createdNodes.push(propertyNode);
            }

            // Create relationship
            const relationship = await this.createRelationship(
              supplementNode.id,
              propertyNode.id,
              'HAS_PROPERTY',
              {
                strength: property.strength,
                evidence: property.evidence,
                mechanism: property.mechanism,
                source: 'AI_ANALYSIS'
              }
            );
            createdRelationships.push(relationship);
          } catch (error) {
            logger.warn(`Failed to process property: ${property.name}`, error);
          }
        }
      }

      // Add research data as source nodes
      if (researchData && researchData.researchResults) {
        for (const result of researchData.researchResults.slice(0, 5)) { // Limit to top 5 results
          try {
            const sourceNode = await this.createNode('ResearchSource', {
              title: result.title,
              url: result.url,
              domain: result.domain,
              relevanceScore: result.relevanceScore,
              snippet: result.snippet
            });
            createdNodes.push(sourceNode);

            // Create relationship
            const relationship = await this.createRelationship(
              supplementNode.id,
              sourceNode.id,
              'SUPPORTED_BY',
              {
                relevanceScore: result.relevanceScore,
                source: 'RESEARCH_DATA'
              }
            );
            createdRelationships.push(relationship);
          } catch (error) {
            logger.warn(`Failed to process research source: ${result.title}`, error);
          }
        }
      }

      const duration = Date.now() - startTime;
      logGraphOperation('Update supplement knowledge', createdNodes.length, createdRelationships.length, duration);

      return {
        supplementNode,
        createdNodes: createdNodes.length,
        createdRelationships: createdRelationships.length,
        healthDomains: analysisData.healthDomains?.length || 0,
        properties: analysisData.properties?.length || 0,
        researchSources: researchData?.researchResults?.length || 0,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      logError('Failed to update supplement knowledge', error, { supplementName });
      throw new DatabaseError('Failed to update supplement knowledge in graph');
    }
  }

  private getEntityTypesForExtraction(extractionType?: string): string[] {
    switch (extractionType) {
      case 'supplement':
        return ['Supplement', 'Ingredient', 'Effect', 'Dosage'];
      case 'ingredient':
        return ['Ingredient', 'Effect', 'Interaction', 'Source'];
      case 'study':
        return ['Study', 'Supplement', 'Effect', 'Participant'];
      default:
        return ['Supplement', 'Ingredient', 'Effect', 'Study', 'Interaction'];
    }
  }

  private async invalidateGraphCaches(): Promise<void> {
    try {
      // Get all cache keys that start with 'graph:' or 'node:'
      const graphKeys = await cacheGet('graph:*') || [];
      const nodeKeys = await cacheGet('node:*') || [];
      const searchKeys = await cacheGet('search:*') || [];
      
      const allKeys = [...graphKeys, ...nodeKeys, ...searchKeys];
      
      if (allKeys.length > 0) {
        await cacheDel(allKeys);
      }
    } catch (error) {
      logger.warn('Failed to invalidate graph caches', error);
    }
  }
}
