import { getWeaviateClient, addWeaviateObject, searchWeaviateSimilar } from '@/config/weaviate';
import { cacheGet, cacheSet } from '@/config/redis';
import { AIService } from '@/services/AIService';
import { config } from '@/config/environment';
import { logger, logDatabaseOperation, logError } from '@/utils/logger';
import { DatabaseError, NotFoundError } from '@/middleware/errorHandler';

interface QueryOptions {
  maxResults?: number;
  similarityThreshold?: number;
  includeMetadata?: boolean;
  context?: any[];
}

interface SimilarityOptions {
  className?: string;
  limit?: number;
  threshold?: number;
}

interface DocumentData {
  content: string;
  title?: string;
  source?: string;
  metadata?: any;
  className?: string;
}

interface SearchOptions {
  className?: string;
  limit?: number;
  offset?: number;
}

interface AnswerOptions {
  context?: any[];
  useGraph?: boolean;
  maxContextLength?: number;
}

interface ReindexOptions {
  className?: string;
  batchSize?: number;
}

export class RAGService {
  private aiService: AIService;

  constructor() {
    this.aiService = new AIService();
  }

  async queryKnowledgeBase(question: string, options: QueryOptions): Promise<any> {
    const startTime = Date.now();
    const cacheKey = `rag:query:${this.hashString(question)}:${JSON.stringify(options)}`;

    try {
      // Try cache first
      const cached = await cacheGet(cacheKey);
      if (cached) {
        return cached;
      }

      // Search for relevant documents across all classes
      const searchPromises = [
        this.findSimilarByText(question, { className: 'Supplement', limit: options.maxResults || 5 }),
        this.findSimilarByText(question, { className: 'Ingredient', limit: options.maxResults || 5 }),
        this.findSimilarByText(question, { className: 'Study', limit: options.maxResults || 5 }),
        this.findSimilarByText(question, { className: 'Effect', limit: options.maxResults || 5 }),
      ];

      const searchResults = await Promise.all(searchPromises);
      
      // Combine and filter results by similarity threshold
      const allResults = searchResults.flat().filter(result => 
        result._additional?.certainty >= (options.similarityThreshold || config.rag.similarityThreshold)
      );

      // Sort by relevance and limit results
      const sortedResults = allResults
        .sort((a, b) => (b._additional?.certainty || 0) - (a._additional?.certainty || 0))
        .slice(0, options.maxResults || 10);

      // Prepare context for answer generation
      const context = sortedResults.map(result => ({
        content: result.content || result.description || result.name,
        source: result.source,
        certainty: result._additional?.certainty,
        metadata: options.includeMetadata ? result : undefined,
      }));

      const result = {
        question,
        results: sortedResults,
        context,
        totalResults: allResults.length,
        timestamp: new Date().toISOString(),
      };

      // Cache the result
      await cacheSet(cacheKey, result, config.cache.ttl);

      const duration = Date.now() - startTime;
      logDatabaseOperation('RAG query', 'Weaviate', duration, {
        question: question.substring(0, 100),
        resultCount: sortedResults.length,
      });

      return result;
    } catch (error) {
      logError('Failed to query knowledge base', error, { question, options });
      throw new DatabaseError('Knowledge base query failed');
    }
  }

  async createEmbedding(text: string, metadata: any = {}, className: string = 'Document'): Promise<any> {
    const startTime = Date.now();

    try {
      const properties = {
        content: text,
        ...metadata,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      const objectId = await addWeaviateObject(className, properties);

      const duration = Date.now() - startTime;
      logDatabaseOperation('Create embedding', `Weaviate:${className}`, duration, {
        objectId,
        textLength: text.length,
      });

      return {
        id: objectId,
        className,
        properties,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      logError('Failed to create embedding', error, { className, metadata });
      throw new DatabaseError('Embedding creation failed');
    }
  }

  async findSimilarByText(text: string, options: SimilarityOptions): Promise<any[]> {
    const startTime = Date.now();
    const cacheKey = `rag:similar:${this.hashString(text)}:${JSON.stringify(options)}`;

    try {
      // Try cache first
      const cached = await cacheGet(cacheKey);
      if (cached) {
        return cached;
      }

      const className = options.className || 'Document';
      const limit = options.limit || 10;

      const results = await searchWeaviateSimilar(className, text, limit);

      // Filter by threshold if specified
      const filteredResults = options.threshold
        ? results.filter((result: any) => result._additional?.certainty >= options.threshold!)
        : results;

      // Cache the result
      await cacheSet(cacheKey, filteredResults, config.cache.ttl);

      const duration = Date.now() - startTime;
      logDatabaseOperation('Find similar by text', `Weaviate:${className}`, duration, {
        query: text.substring(0, 100),
        resultCount: filteredResults.length,
      });

      return filteredResults;
    } catch (error) {
      logError('Failed to find similar by text', error, { text, options });
      throw new DatabaseError('Similarity search failed');
    }
  }

  async findSimilarByVector(vector: number[], options: SimilarityOptions): Promise<any[]> {
    const startTime = Date.now();

    try {
      const client = getWeaviateClient();
      const className = options.className || 'Document';
      const limit = options.limit || 10;

      const result = await client.graphql
        .get()
        .withClassName(className)
        .withNearVector({ vector })
        .withLimit(limit)
        .withFields('_additional { certainty distance } name description content')
        .do();

      const results = result.data.Get[className] || [];

      // Filter by threshold if specified
      const filteredResults = options.threshold
        ? results.filter((result: any) => result._additional?.certainty >= options.threshold!)
        : results;

      const duration = Date.now() - startTime;
      logDatabaseOperation('Find similar by vector', `Weaviate:${className}`, duration, {
        vectorDimension: vector.length,
        resultCount: filteredResults.length,
      });

      return filteredResults;
    } catch (error) {
      logError('Failed to find similar by vector', error, { options });
      throw new DatabaseError('Vector similarity search failed');
    }
  }

  async addDocument(documentData: DocumentData): Promise<any> {
    const startTime = Date.now();

    try {
      const className = documentData.className || 'Document';
      const properties = {
        content: documentData.content,
        title: documentData.title,
        source: documentData.source,
        ...documentData.metadata,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      const objectId = await addWeaviateObject(className, properties);

      const duration = Date.now() - startTime;
      logDatabaseOperation('Add document', `Weaviate:${className}`, duration, {
        objectId,
        contentLength: documentData.content.length,
      });

      return {
        id: objectId,
        className,
        properties,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      logError('Failed to add document', error, { documentData });
      throw new DatabaseError('Document addition failed');
    }
  }

  async getDocument(id: string): Promise<any> {
    const startTime = Date.now();
    const cacheKey = `rag:document:${id}`;

    try {
      // Try cache first
      const cached = await cacheGet(cacheKey);
      if (cached) {
        return cached;
      }

      const client = getWeaviateClient();
      
      const result = await client.data
        .getterById()
        .withId(id)
        .do();

      if (!result) {
        throw new NotFoundError(`Document with id ${id} not found`);
      }

      // Cache the result
      await cacheSet(cacheKey, result, config.cache.ttl);

      const duration = Date.now() - startTime;
      logDatabaseOperation('Get document', 'Weaviate', duration, { id });

      return result;
    } catch (error) {
      if (error instanceof NotFoundError) throw error;
      logError('Failed to get document', error, { id });
      throw new DatabaseError('Document retrieval failed');
    }
  }

  async updateDocument(id: string, updates: Partial<DocumentData>): Promise<any> {
    const startTime = Date.now();

    try {
      const client = getWeaviateClient();
      
      const updateProperties = {
        ...updates,
        updatedAt: new Date().toISOString(),
      };

      const result = await client.data
        .updater()
        .withId(id)
        .withProperties(updateProperties)
        .do();

      // Invalidate cache
      await this.invalidateDocumentCache(id);

      const duration = Date.now() - startTime;
      logDatabaseOperation('Update document', 'Weaviate', duration, { id });

      return result;
    } catch (error) {
      logError('Failed to update document', error, { id, updates });
      throw new DatabaseError('Document update failed');
    }
  }

  async deleteDocument(id: string): Promise<void> {
    const startTime = Date.now();

    try {
      const client = getWeaviateClient();
      
      await client.data
        .deleter()
        .withId(id)
        .do();

      // Invalidate cache
      await this.invalidateDocumentCache(id);

      const duration = Date.now() - startTime;
      logDatabaseOperation('Delete document', 'Weaviate', duration, { id });
    } catch (error) {
      logError('Failed to delete document', error, { id });
      throw new DatabaseError('Document deletion failed');
    }
  }

  async searchDocuments(query: string, options: SearchOptions): Promise<any> {
    const startTime = Date.now();
    const cacheKey = `rag:search:${this.hashString(query)}:${JSON.stringify(options)}`;

    try {
      // Try cache first
      const cached = await cacheGet(cacheKey);
      if (cached) {
        return cached;
      }

      const className = options.className || 'Document';
      const limit = options.limit || 20;
      const offset = options.offset || 0;

      const results = await searchWeaviateSimilar(className, query, limit + offset);
      
      // Apply pagination
      const paginatedResults = results.slice(offset, offset + limit);

      const searchResult = {
        query,
        results: paginatedResults,
        totalResults: results.length,
        limit,
        offset,
        hasMore: results.length > offset + limit,
        timestamp: new Date().toISOString(),
      };

      // Cache the result
      await cacheSet(cacheKey, searchResult, config.cache.ttl);

      const duration = Date.now() - startTime;
      logDatabaseOperation('Search documents', `Weaviate:${className}`, duration, {
        query: query.substring(0, 100),
        resultCount: paginatedResults.length,
      });

      return searchResult;
    } catch (error) {
      logError('Failed to search documents', error, { query, options });
      throw new DatabaseError('Document search failed');
    }
  }

  async listDocuments(options: SearchOptions): Promise<any> {
    const startTime = Date.now();
    const cacheKey = `rag:list:${JSON.stringify(options)}`;

    try {
      // Try cache first
      const cached = await cacheGet(cacheKey);
      if (cached) {
        return cached;
      }

      const client = getWeaviateClient();
      const className = options.className || 'Document';
      const limit = options.limit || 20;
      const offset = options.offset || 0;

      const result = await client.graphql
        .get()
        .withClassName(className)
        .withFields('_additional { id } content title source createdAt updatedAt')
        .withLimit(limit)
        .withOffset(offset)
        .do();

      const documents = result.data.Get[className] || [];

      const listResult = {
        documents,
        totalResults: documents.length,
        limit,
        offset,
        hasMore: documents.length === limit,
        timestamp: new Date().toISOString(),
      };

      // Cache the result for shorter time
      await cacheSet(cacheKey, listResult, 300); // 5 minutes

      const duration = Date.now() - startTime;
      logDatabaseOperation('List documents', `Weaviate:${className}`, duration, {
        resultCount: documents.length,
      });

      return listResult;
    } catch (error) {
      logError('Failed to list documents', error, { options });
      throw new DatabaseError('Document listing failed');
    }
  }

  async generateAnswer(question: string, options: AnswerOptions): Promise<any> {
    const startTime = Date.now();

    try {
      // Get relevant context from knowledge base
      const queryResult = await this.queryKnowledgeBase(question, {
        maxResults: 10,
        similarityThreshold: 0.7,
        includeMetadata: false,
      });

      // Prepare context for AI
      let contextText = queryResult.context
        .map((ctx: any) => ctx.content)
        .join('\n\n')
        .substring(0, options.maxContextLength || config.rag.maxContextLength);

      // Add user-provided context if any
      if (options.context && options.context.length > 0) {
        const userContext = options.context.map(ctx => ctx.content || ctx).join('\n\n');
        contextText = `${userContext}\n\n${contextText}`;
      }

      // Generate answer using AI
      const prompt = `
        Based on the following context about supplements and health, please answer the question.

        Context:
        ${contextText}

        Question: ${question}

        Please provide a comprehensive, accurate answer based on the context provided. If the context doesn't contain enough information to answer the question fully, please indicate this and suggest what additional information might be needed.

        Include appropriate medical disclaimers when discussing health topics.
      `;

      const aiResponse = await this.aiService.chat(prompt, {
        temperature: 0.3, // Lower temperature for more factual responses
      });

      const answer = {
        question,
        answer: aiResponse.message,
        sources: queryResult.results.map(result => ({
          content: result.content || result.description || result.name,
          source: result.source,
          certainty: result._additional?.certainty,
        })),
        contextUsed: contextText.length,
        totalSources: queryResult.totalResults,
        timestamp: new Date().toISOString(),
        disclaimer: 'This information is for educational purposes only and should not replace professional medical advice.',
      };

      const duration = Date.now() - startTime;
      logDatabaseOperation('Generate answer', 'RAG', duration, {
        question: question.substring(0, 100),
        sourcesUsed: queryResult.results.length,
      });

      return answer;
    } catch (error) {
      logError('Failed to generate answer', error, { question, options });
      throw new DatabaseError('Answer generation failed');
    }
  }

  async getStats(): Promise<any> {
    const startTime = Date.now();
    const cacheKey = 'rag:stats';

    try {
      // Try cache first
      const cached = await cacheGet(cacheKey);
      if (cached) {
        return cached;
      }

      const client = getWeaviateClient();
      const schema = await client.schema.getter().do();

      const stats: any = {
        totalClasses: schema.classes?.length || 0,
        classDetails: [],
        lastUpdated: new Date().toISOString(),
      };

      if (schema.classes) {
        for (const cls of schema.classes) {
          try {
            const result = await client.graphql
              .aggregate()
              .withClassName(cls.class!)
              .withFields('meta { count }')
              .do();

            stats.classDetails.push({
              name: cls.class!,
              count: result.data.Aggregate[cls.class!]?.[0]?.meta?.count || 0,
              description: cls.description,
            });
          } catch (error) {
            stats.classDetails.push({
              name: cls.class,
              count: 0,
              error: 'Failed to get count',
            });
          }
        }
      }

      // Cache for 5 minutes
      await cacheSet(cacheKey, stats, 300);

      const duration = Date.now() - startTime;
      logDatabaseOperation('Get RAG stats', 'Weaviate', duration);

      return stats;
    } catch (error) {
      logError('Failed to get RAG stats', error);
      throw new DatabaseError('Failed to get RAG statistics');
    }
  }

  async reindexDocuments(options: ReindexOptions): Promise<any> {
    const startTime = Date.now();

    try {
      const client = getWeaviateClient();
      const className = options.className;
      const batchSize = options.batchSize || 100;

      let totalProcessed = 0;
      let totalErrors = 0;

      if (className) {
        // Reindex specific class
        const result = await this.reindexClass(className, batchSize);
        totalProcessed += result.processed;
        totalErrors += result.errors;
      } else {
        // Reindex all classes
        const schema = await client.schema.getter().do();

        if (schema.classes) {
          for (const cls of schema.classes) {
            try {
              const result = await this.reindexClass(cls.class!, batchSize);
              totalProcessed += result.processed;
              totalErrors += result.errors;
            } catch (error) {
              logger.warn(`Failed to reindex class: ${cls.class}`, error);
              totalErrors++;
            }
          }
        }
      }

      const duration = Date.now() - startTime;
      logDatabaseOperation('Reindex documents', 'Weaviate', duration, {
        totalProcessed,
        totalErrors,
      });

      return {
        totalProcessed,
        totalErrors,
        duration,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      logError('Failed to reindex documents', error, { options });
      throw new DatabaseError('Document reindexing failed');
    }
  }

  private async reindexClass(className: string, batchSize: number): Promise<{ processed: number; errors: number }> {
    const client = getWeaviateClient();
    let processed = 0;
    let errors = 0;
    let offset = 0;

    while (true) {
      try {
        const result = await client.graphql
          .get()
          .withClassName(className)
          .withFields('_additional { id } content title source')
          .withLimit(batchSize)
          .withOffset(offset)
          .do();

        const objects = result.data.Get[className] || [];

        if (objects.length === 0) {
          break; // No more objects to process
        }

        // Process batch (this is a simplified reindexing - in practice, you might want to update embeddings)
        for (const obj of objects) {
          try {
            // Update the object to trigger re-embedding
            await client.data
              .updater()
              .withId(obj._additional.id)
              .withProperties({
                ...obj,
                reindexedAt: new Date().toISOString(),
              })
              .do();

            processed++;
          } catch (error) {
            errors++;
            logger.warn(`Failed to reindex object: ${obj._additional.id}`, error);
          }
        }

        offset += batchSize;

        // Add small delay to avoid overwhelming the system
        await new Promise(resolve => setTimeout(resolve, 100));
      } catch (error) {
        logger.error(`Failed to process batch for class: ${className}`, error);
        errors++;
        break;
      }
    }

    return { processed, errors };
  }

  private async invalidateDocumentCache(id: string): Promise<void> {
    try {
      await Promise.all([
        this.invalidateCache(`rag:document:${id}`),
        this.invalidateCache('rag:list:*'),
        this.invalidateCache('rag:search:*'),
        this.invalidateCache('rag:query:*'),
      ]);
    } catch (error) {
      logger.warn('Failed to invalidate document cache', error);
    }
  }

  private async invalidateCache(pattern: string): Promise<void> {
    try {
      if (pattern.includes('*')) {
        // This is a simplified approach - in practice, you'd want to use Redis SCAN
        // For now, we'll just clear the stats cache
        if (pattern.includes('rag:')) {
          await cacheSet('rag:stats', null, 1); // Expire immediately
        }
      } else {
        await cacheSet(pattern, null, 1); // Expire immediately
      }
    } catch (error) {
      logger.warn(`Failed to invalidate cache pattern: ${pattern}`, error);
    }
  }

  private hashString(str: string): string {
    const crypto = require('crypto');
    return crypto.createHash('md5').update(str).digest('hex');
  }
}
