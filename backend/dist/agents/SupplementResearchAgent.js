"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SupplementResearchAgent = void 0;
const client_1 = require("@ag-ui/client");
const rxjs_1 = require("rxjs");
const ResearchService_1 = require("../services/ResearchService");
const GemmaService_1 = require("../services/GemmaService");
const GraphService_1 = require("../services/GraphService");
const logger_1 = require("../utils/logger");
class SupplementResearchAgent extends client_1.AbstractAgent {
    researchService;
    gemmaService;
    graphService;
    constructor() {
        super();
        this.researchService = new ResearchService_1.ResearchService();
        this.gemmaService = new GemmaService_1.GemmaService();
        this.graphService = new GraphService_1.GraphService();
    }
    run(input) {
        const supplementInput = input;
        return new rxjs_1.Observable((observer) => {
            this.executeResearchPipeline(supplementInput, observer);
        });
    }
    runSupplementResearch(input) {
        return this.run(input);
    }
    async executeResearchPipeline(input, observer) {
        const messageId = Date.now().toString();
        try {
            observer.next({
                type: client_1.EventType.RUN_STARTED,
                threadId: input.threadId,
                runId: input.runId,
            });
            observer.next({
                type: client_1.EventType.TEXT_MESSAGE_START,
                messageId,
            });
            observer.next({
                type: client_1.EventType.TEXT_MESSAGE_CONTENT,
                messageId,
                delta: `🔍 Starting research for supplement: ${input.supplementName}\n`,
            });
            observer.next({
                type: client_1.EventType.TOOL_CALL_START,
                toolCallId: `research-${Date.now()}`,
                toolCallName: 'tavily_research',
                parentMessageId: messageId,
            });
            const researchData = await this.researchService.gatherSupplementResearch(input.supplementName, {
                includeInteractions: input.includeInteractions || false,
                researchDepth: input.researchDepth || 'basic'
            });
            observer.next({
                type: client_1.EventType.TOOL_CALL_END,
                toolCallId: `research-${Date.now()}`,
                toolCallName: 'tavily_research',
                parentMessageId: messageId,
            });
            observer.next({
                type: client_1.EventType.TEXT_MESSAGE_CONTENT,
                messageId,
                delta: `✅ Gathered ${researchData.researchResults?.length || 0} research sources\n`,
            });
            observer.next({
                type: client_1.EventType.TOOL_CALL_START,
                toolCallId: `gemma-analysis-${Date.now()}`,
                toolCallName: 'gemma_analysis',
                parentMessageId: messageId,
            });
            const analysis = await this.gemmaService.analyzeSupplementData(input.supplementName, researchData);
            observer.next({
                type: client_1.EventType.TOOL_CALL_END,
                toolCallId: `gemma-analysis-${Date.now()}`,
                toolCallName: 'gemma_analysis',
                parentMessageId: messageId,
            });
            observer.next({
                type: client_1.EventType.TEXT_MESSAGE_CONTENT,
                messageId,
                delta: `🧠 AI Analysis complete. Identified ${analysis.healthDomains.length} health domains\n`,
            });
            observer.next({
                type: client_1.EventType.TOOL_CALL_START,
                toolCallId: `graph-update-${Date.now()}`,
                toolCallName: 'update_knowledge_graph',
                parentMessageId: messageId,
            });
            const graphUpdate = await this.graphService.updateSupplementKnowledge(input.supplementName, analysis, researchData);
            observer.next({
                type: client_1.EventType.TOOL_CALL_END,
                toolCallId: `graph-update-${Date.now()}`,
                toolCallName: 'update_knowledge_graph',
                parentMessageId: messageId,
            });
            observer.next({
                type: client_1.EventType.STATE_DELTA,
                delta: {
                    supplement: {
                        name: input.supplementName,
                        nodes: graphUpdate.createdNodes || 0,
                        edges: graphUpdate.createdRelationships || 0,
                        healthDomains: analysis.healthDomains,
                        properties: analysis.properties,
                    },
                },
            });
            observer.next({
                type: client_1.EventType.TEXT_MESSAGE_CONTENT,
                messageId,
                delta: `📊 Knowledge graph updated with ${graphUpdate.createdNodes || 0} nodes and ${graphUpdate.createdRelationships || 0} connections\n`,
            });
            const insights = await this.gemmaService.generateInsights(input.supplementName, analysis, graphUpdate);
            observer.next({
                type: client_1.EventType.TEXT_MESSAGE_CONTENT,
                messageId,
                delta: `\n💡 **Key Insights:**\n${insights.summary}\n\n`,
            });
            insights.keyFindings.forEach((finding, index) => {
                observer.next({
                    type: client_1.EventType.TEXT_MESSAGE_CONTENT,
                    messageId,
                    delta: `${index + 1}. ${finding}\n`,
                });
            });
            if (insights.warnings.length > 0) {
                observer.next({
                    type: client_1.EventType.TEXT_MESSAGE_CONTENT,
                    messageId,
                    delta: `\n⚠️ **Important Considerations:**\n`,
                });
                insights.warnings.forEach((warning, index) => {
                    observer.next({
                        type: client_1.EventType.TEXT_MESSAGE_CONTENT,
                        messageId,
                        delta: `${index + 1}. ${warning}\n`,
                    });
                });
            }
            observer.next({
                type: client_1.EventType.TEXT_MESSAGE_CONTENT,
                messageId,
                delta: `\n🎯 Research complete! The knowledge graph has been updated with comprehensive information about ${input.supplementName}.`,
            });
            observer.next({
                type: client_1.EventType.TEXT_MESSAGE_END,
                messageId,
            });
            observer.next({
                type: client_1.EventType.RUN_FINISHED,
                threadId: input.threadId,
                runId: input.runId,
            });
            observer.complete();
        }
        catch (error) {
            logger_1.logger.error('Error in supplement research pipeline:', error);
            observer.next({
                type: client_1.EventType.RUN_ERROR,
                message: `Research failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
            });
            observer.error(error);
        }
    }
}
exports.SupplementResearchAgent = SupplementResearchAgent;
//# sourceMappingURL=SupplementResearchAgent.js.map