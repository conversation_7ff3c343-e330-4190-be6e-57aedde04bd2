"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RAGService = void 0;
const weaviate_1 = require("@/config/weaviate");
const redis_1 = require("@/config/redis");
const AIService_1 = require("@/services/AIService");
const environment_1 = require("@/config/environment");
const logger_1 = require("@/utils/logger");
const errorHandler_1 = require("@/middleware/errorHandler");
class RAGService {
    aiService;
    constructor() {
        this.aiService = new AIService_1.AIService();
    }
    async queryKnowledgeBase(question, options) {
        const startTime = Date.now();
        const cacheKey = `rag:query:${this.hashString(question)}:${JSON.stringify(options)}`;
        try {
            const cached = await (0, redis_1.cacheGet)(cacheKey);
            if (cached) {
                return cached;
            }
            const searchPromises = [
                this.findSimilarByText(question, { className: 'Supplement', limit: options.maxResults || 5 }),
                this.findSimilarByText(question, { className: 'Ingredient', limit: options.maxResults || 5 }),
                this.findSimilarByText(question, { className: 'Study', limit: options.maxResults || 5 }),
                this.findSimilarByText(question, { className: 'Effect', limit: options.maxResults || 5 }),
            ];
            const searchResults = await Promise.all(searchPromises);
            const allResults = searchResults.flat().filter(result => result._additional?.certainty >= (options.similarityThreshold || environment_1.config.rag.similarityThreshold));
            const sortedResults = allResults
                .sort((a, b) => (b._additional?.certainty || 0) - (a._additional?.certainty || 0))
                .slice(0, options.maxResults || 10);
            const context = sortedResults.map(result => ({
                content: result.content || result.description || result.name,
                source: result.source,
                certainty: result._additional?.certainty,
                metadata: options.includeMetadata ? result : undefined,
            }));
            const result = {
                question,
                results: sortedResults,
                context,
                totalResults: allResults.length,
                timestamp: new Date().toISOString(),
            };
            await (0, redis_1.cacheSet)(cacheKey, result, environment_1.config.cache.ttl);
            const duration = Date.now() - startTime;
            (0, logger_1.logDatabaseOperation)('RAG query', 'Weaviate', duration, {
                question: question.substring(0, 100),
                resultCount: sortedResults.length,
            });
            return result;
        }
        catch (error) {
            (0, logger_1.logError)('Failed to query knowledge base', error, { question, options });
            throw new errorHandler_1.DatabaseError('Knowledge base query failed');
        }
    }
    async createEmbedding(text, metadata = {}, className = 'Document') {
        const startTime = Date.now();
        try {
            const properties = {
                content: text,
                ...metadata,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
            };
            const objectId = await (0, weaviate_1.addWeaviateObject)(className, properties);
            const duration = Date.now() - startTime;
            (0, logger_1.logDatabaseOperation)('Create embedding', `Weaviate:${className}`, duration, {
                objectId,
                textLength: text.length,
            });
            return {
                id: objectId,
                className,
                properties,
                timestamp: new Date().toISOString(),
            };
        }
        catch (error) {
            (0, logger_1.logError)('Failed to create embedding', error, { className, metadata });
            throw new errorHandler_1.DatabaseError('Embedding creation failed');
        }
    }
    async findSimilarByText(text, options) {
        const startTime = Date.now();
        const cacheKey = `rag:similar:${this.hashString(text)}:${JSON.stringify(options)}`;
        try {
            const cached = await (0, redis_1.cacheGet)(cacheKey);
            if (cached) {
                return cached;
            }
            const className = options.className || 'Document';
            const limit = options.limit || 10;
            const results = await (0, weaviate_1.searchWeaviateSimilar)(className, text, limit);
            const filteredResults = options.threshold
                ? results.filter((result) => result._additional?.certainty >= options.threshold)
                : results;
            await (0, redis_1.cacheSet)(cacheKey, filteredResults, environment_1.config.cache.ttl);
            const duration = Date.now() - startTime;
            (0, logger_1.logDatabaseOperation)('Find similar by text', `Weaviate:${className}`, duration, {
                query: text.substring(0, 100),
                resultCount: filteredResults.length,
            });
            return filteredResults;
        }
        catch (error) {
            (0, logger_1.logError)('Failed to find similar by text', error, { text, options });
            throw new errorHandler_1.DatabaseError('Similarity search failed');
        }
    }
    async findSimilarByVector(vector, options) {
        const startTime = Date.now();
        try {
            const client = (0, weaviate_1.getWeaviateClient)();
            const className = options.className || 'Document';
            const limit = options.limit || 10;
            const result = await client.graphql
                .get()
                .withClassName(className)
                .withNearVector({ vector })
                .withLimit(limit)
                .withFields('_additional { certainty distance } name description content')
                .do();
            const results = result.data.Get[className] || [];
            const filteredResults = options.threshold
                ? results.filter((result) => result._additional?.certainty >= options.threshold)
                : results;
            const duration = Date.now() - startTime;
            (0, logger_1.logDatabaseOperation)('Find similar by vector', `Weaviate:${className}`, duration, {
                vectorDimension: vector.length,
                resultCount: filteredResults.length,
            });
            return filteredResults;
        }
        catch (error) {
            (0, logger_1.logError)('Failed to find similar by vector', error, { options });
            throw new errorHandler_1.DatabaseError('Vector similarity search failed');
        }
    }
    async addDocument(documentData) {
        const startTime = Date.now();
        try {
            const className = documentData.className || 'Document';
            const properties = {
                content: documentData.content,
                title: documentData.title,
                source: documentData.source,
                ...documentData.metadata,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
            };
            const objectId = await (0, weaviate_1.addWeaviateObject)(className, properties);
            const duration = Date.now() - startTime;
            (0, logger_1.logDatabaseOperation)('Add document', `Weaviate:${className}`, duration, {
                objectId,
                contentLength: documentData.content.length,
            });
            return {
                id: objectId,
                className,
                properties,
                timestamp: new Date().toISOString(),
            };
        }
        catch (error) {
            (0, logger_1.logError)('Failed to add document', error, { documentData });
            throw new errorHandler_1.DatabaseError('Document addition failed');
        }
    }
    async getDocument(id) {
        const startTime = Date.now();
        const cacheKey = `rag:document:${id}`;
        try {
            const cached = await (0, redis_1.cacheGet)(cacheKey);
            if (cached) {
                return cached;
            }
            const client = (0, weaviate_1.getWeaviateClient)();
            const result = await client.data
                .getterById()
                .withId(id)
                .do();
            if (!result) {
                throw new errorHandler_1.NotFoundError(`Document with id ${id} not found`);
            }
            await (0, redis_1.cacheSet)(cacheKey, result, environment_1.config.cache.ttl);
            const duration = Date.now() - startTime;
            (0, logger_1.logDatabaseOperation)('Get document', 'Weaviate', duration, { id });
            return result;
        }
        catch (error) {
            if (error instanceof errorHandler_1.NotFoundError)
                throw error;
            (0, logger_1.logError)('Failed to get document', error, { id });
            throw new errorHandler_1.DatabaseError('Document retrieval failed');
        }
    }
    async updateDocument(id, updates) {
        const startTime = Date.now();
        try {
            const client = (0, weaviate_1.getWeaviateClient)();
            const updateProperties = {
                ...updates,
                updatedAt: new Date().toISOString(),
            };
            const result = await client.data
                .updater()
                .withId(id)
                .withProperties(updateProperties)
                .do();
            await this.invalidateDocumentCache(id);
            const duration = Date.now() - startTime;
            (0, logger_1.logDatabaseOperation)('Update document', 'Weaviate', duration, { id });
            return result;
        }
        catch (error) {
            (0, logger_1.logError)('Failed to update document', error, { id, updates });
            throw new errorHandler_1.DatabaseError('Document update failed');
        }
    }
    async deleteDocument(id) {
        const startTime = Date.now();
        try {
            const client = (0, weaviate_1.getWeaviateClient)();
            await client.data
                .deleter()
                .withId(id)
                .do();
            await this.invalidateDocumentCache(id);
            const duration = Date.now() - startTime;
            (0, logger_1.logDatabaseOperation)('Delete document', 'Weaviate', duration, { id });
        }
        catch (error) {
            (0, logger_1.logError)('Failed to delete document', error, { id });
            throw new errorHandler_1.DatabaseError('Document deletion failed');
        }
    }
    async searchDocuments(query, options) {
        const startTime = Date.now();
        const cacheKey = `rag:search:${this.hashString(query)}:${JSON.stringify(options)}`;
        try {
            const cached = await (0, redis_1.cacheGet)(cacheKey);
            if (cached) {
                return cached;
            }
            const className = options.className || 'Document';
            const limit = options.limit || 20;
            const offset = options.offset || 0;
            const results = await (0, weaviate_1.searchWeaviateSimilar)(className, query, limit + offset);
            const paginatedResults = results.slice(offset, offset + limit);
            const searchResult = {
                query,
                results: paginatedResults,
                totalResults: results.length,
                limit,
                offset,
                hasMore: results.length > offset + limit,
                timestamp: new Date().toISOString(),
            };
            await (0, redis_1.cacheSet)(cacheKey, searchResult, environment_1.config.cache.ttl);
            const duration = Date.now() - startTime;
            (0, logger_1.logDatabaseOperation)('Search documents', `Weaviate:${className}`, duration, {
                query: query.substring(0, 100),
                resultCount: paginatedResults.length,
            });
            return searchResult;
        }
        catch (error) {
            (0, logger_1.logError)('Failed to search documents', error, { query, options });
            throw new errorHandler_1.DatabaseError('Document search failed');
        }
    }
    async listDocuments(options) {
        const startTime = Date.now();
        const cacheKey = `rag:list:${JSON.stringify(options)}`;
        try {
            const cached = await (0, redis_1.cacheGet)(cacheKey);
            if (cached) {
                return cached;
            }
            const client = (0, weaviate_1.getWeaviateClient)();
            const className = options.className || 'Document';
            const limit = options.limit || 20;
            const offset = options.offset || 0;
            const result = await client.graphql
                .get()
                .withClassName(className)
                .withFields('_additional { id } content title source createdAt updatedAt')
                .withLimit(limit)
                .withOffset(offset)
                .do();
            const documents = result.data.Get[className] || [];
            const listResult = {
                documents,
                totalResults: documents.length,
                limit,
                offset,
                hasMore: documents.length === limit,
                timestamp: new Date().toISOString(),
            };
            await (0, redis_1.cacheSet)(cacheKey, listResult, 300);
            const duration = Date.now() - startTime;
            (0, logger_1.logDatabaseOperation)('List documents', `Weaviate:${className}`, duration, {
                resultCount: documents.length,
            });
            return listResult;
        }
        catch (error) {
            (0, logger_1.logError)('Failed to list documents', error, { options });
            throw new errorHandler_1.DatabaseError('Document listing failed');
        }
    }
    async generateAnswer(question, options) {
        const startTime = Date.now();
        try {
            const queryResult = await this.queryKnowledgeBase(question, {
                maxResults: 10,
                similarityThreshold: 0.7,
                includeMetadata: false,
            });
            let contextText = queryResult.context
                .map((ctx) => ctx.content)
                .join('\n\n')
                .substring(0, options.maxContextLength || environment_1.config.rag.maxContextLength);
            if (options.context && options.context.length > 0) {
                const userContext = options.context.map(ctx => ctx.content || ctx).join('\n\n');
                contextText = `${userContext}\n\n${contextText}`;
            }
            const prompt = `
        Based on the following context about supplements and health, please answer the question.

        Context:
        ${contextText}

        Question: ${question}

        Please provide a comprehensive, accurate answer based on the context provided. If the context doesn't contain enough information to answer the question fully, please indicate this and suggest what additional information might be needed.

        Include appropriate medical disclaimers when discussing health topics.
      `;
            const aiResponse = await this.aiService.chat(prompt, {
                temperature: 0.3,
            });
            const answer = {
                question,
                answer: aiResponse.message,
                sources: queryResult.results.map(result => ({
                    content: result.content || result.description || result.name,
                    source: result.source,
                    certainty: result._additional?.certainty,
                })),
                contextUsed: contextText.length,
                totalSources: queryResult.totalResults,
                timestamp: new Date().toISOString(),
                disclaimer: 'This information is for educational purposes only and should not replace professional medical advice.',
            };
            const duration = Date.now() - startTime;
            (0, logger_1.logDatabaseOperation)('Generate answer', 'RAG', duration, {
                question: question.substring(0, 100),
                sourcesUsed: queryResult.results.length,
            });
            return answer;
        }
        catch (error) {
            (0, logger_1.logError)('Failed to generate answer', error, { question, options });
            throw new errorHandler_1.DatabaseError('Answer generation failed');
        }
    }
    async getStats() {
        const startTime = Date.now();
        const cacheKey = 'rag:stats';
        try {
            const cached = await (0, redis_1.cacheGet)(cacheKey);
            if (cached) {
                return cached;
            }
            const client = (0, weaviate_1.getWeaviateClient)();
            const schema = await client.schema.getter().do();
            const stats = {
                totalClasses: schema.classes?.length || 0,
                classDetails: [],
                lastUpdated: new Date().toISOString(),
            };
            if (schema.classes) {
                for (const cls of schema.classes) {
                    try {
                        const result = await client.graphql
                            .aggregate()
                            .withClassName(cls.class)
                            .withFields('meta { count }')
                            .do();
                        stats.classDetails.push({
                            name: cls.class,
                            count: result.data.Aggregate[cls.class]?.[0]?.meta?.count || 0,
                            description: cls.description,
                        });
                    }
                    catch (error) {
                        stats.classDetails.push({
                            name: cls.class,
                            count: 0,
                            error: 'Failed to get count',
                        });
                    }
                }
            }
            await (0, redis_1.cacheSet)(cacheKey, stats, 300);
            const duration = Date.now() - startTime;
            (0, logger_1.logDatabaseOperation)('Get RAG stats', 'Weaviate', duration);
            return stats;
        }
        catch (error) {
            (0, logger_1.logError)('Failed to get RAG stats', error);
            throw new errorHandler_1.DatabaseError('Failed to get RAG statistics');
        }
    }
    async reindexDocuments(options) {
        const startTime = Date.now();
        try {
            const client = (0, weaviate_1.getWeaviateClient)();
            const className = options.className;
            const batchSize = options.batchSize || 100;
            let totalProcessed = 0;
            let totalErrors = 0;
            if (className) {
                const result = await this.reindexClass(className, batchSize);
                totalProcessed += result.processed;
                totalErrors += result.errors;
            }
            else {
                const schema = await client.schema.getter().do();
                if (schema.classes) {
                    for (const cls of schema.classes) {
                        try {
                            const result = await this.reindexClass(cls.class, batchSize);
                            totalProcessed += result.processed;
                            totalErrors += result.errors;
                        }
                        catch (error) {
                            logger_1.logger.warn(`Failed to reindex class: ${cls.class}`, error);
                            totalErrors++;
                        }
                    }
                }
            }
            const duration = Date.now() - startTime;
            (0, logger_1.logDatabaseOperation)('Reindex documents', 'Weaviate', duration, {
                totalProcessed,
                totalErrors,
            });
            return {
                totalProcessed,
                totalErrors,
                duration,
                timestamp: new Date().toISOString(),
            };
        }
        catch (error) {
            (0, logger_1.logError)('Failed to reindex documents', error, { options });
            throw new errorHandler_1.DatabaseError('Document reindexing failed');
        }
    }
    async reindexClass(className, batchSize) {
        const client = (0, weaviate_1.getWeaviateClient)();
        let processed = 0;
        let errors = 0;
        let offset = 0;
        while (true) {
            try {
                const result = await client.graphql
                    .get()
                    .withClassName(className)
                    .withFields('_additional { id } content title source')
                    .withLimit(batchSize)
                    .withOffset(offset)
                    .do();
                const objects = result.data.Get[className] || [];
                if (objects.length === 0) {
                    break;
                }
                for (const obj of objects) {
                    try {
                        await client.data
                            .updater()
                            .withId(obj._additional.id)
                            .withProperties({
                            ...obj,
                            reindexedAt: new Date().toISOString(),
                        })
                            .do();
                        processed++;
                    }
                    catch (error) {
                        errors++;
                        logger_1.logger.warn(`Failed to reindex object: ${obj._additional.id}`, error);
                    }
                }
                offset += batchSize;
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            catch (error) {
                logger_1.logger.error(`Failed to process batch for class: ${className}`, error);
                errors++;
                break;
            }
        }
        return { processed, errors };
    }
    async invalidateDocumentCache(id) {
        try {
            await Promise.all([
                this.invalidateCache(`rag:document:${id}`),
                this.invalidateCache('rag:list:*'),
                this.invalidateCache('rag:search:*'),
                this.invalidateCache('rag:query:*'),
            ]);
        }
        catch (error) {
            logger_1.logger.warn('Failed to invalidate document cache', error);
        }
    }
    async invalidateCache(pattern) {
        try {
            if (pattern.includes('*')) {
                if (pattern.includes('rag:')) {
                    await (0, redis_1.cacheSet)('rag:stats', null, 1);
                }
            }
            else {
                await (0, redis_1.cacheSet)(pattern, null, 1);
            }
        }
        catch (error) {
            logger_1.logger.warn(`Failed to invalidate cache pattern: ${pattern}`, error);
        }
    }
    hashString(str) {
        const crypto = require('crypto');
        return crypto.createHash('md5').update(str).digest('hex');
    }
}
exports.RAGService = RAGService;
//# sourceMappingURL=RAGService.js.map