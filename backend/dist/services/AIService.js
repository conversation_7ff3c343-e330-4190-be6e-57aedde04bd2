"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIService = void 0;
const generative_ai_1 = require("@google/generative-ai");
const openai_1 = __importDefault(require("openai"));
const environment_1 = require("@/config/environment");
const redis_1 = require("@/config/redis");
const logger_1 = require("@/utils/logger");
const errorHandler_1 = require("@/middleware/errorHandler");
const crypto_1 = __importDefault(require("crypto"));
class AIService {
    gemini;
    openai = null;
    constructor() {
        this.gemini = new generative_ai_1.GoogleGenerativeAI(environment_1.config.ai.geminiApiKey);
        if (environment_1.config.ai.openaiApiKey) {
            this.openai = new openai_1.default({
                apiKey: environment_1.config.ai.openaiApiKey,
            });
        }
    }
    async analyzeText(text, options) {
        const startTime = Date.now();
        const cacheKey = `ai:analyze:${this.hashString(text)}:${JSON.stringify(options)}`;
        try {
            const cached = await (0, redis_1.cacheGet)(cacheKey);
            if (cached) {
                return cached;
            }
            const model = this.gemini.getGenerativeModel({
                model: options.model || 'gemini-pro'
            });
            const prompt = this.buildAnalysisPrompt(text, options);
            const result = await model.generateContent(prompt);
            const response = result.response;
            const analysisText = response.text();
            const analysis = this.parseAnalysisResponse(analysisText, options);
            await (0, redis_1.cacheSet)(cacheKey, analysis, environment_1.config.cache.ttl);
            const duration = Date.now() - startTime;
            (0, logger_1.logAiOperation)('Analyze text', options.model || 'gemini-pro', undefined, duration);
            return analysis;
        }
        catch (error) {
            (0, logger_1.logError)('Failed to analyze text', error, { options });
            throw new errorHandler_1.ExternalServiceError('AI analysis failed');
        }
    }
    async extractEntitiesAndRelationships(text, options) {
        const startTime = Date.now();
        const cacheKey = `ai:extract:${this.hashString(text)}:${JSON.stringify(options)}`;
        try {
            const cached = await (0, redis_1.cacheGet)(cacheKey);
            if (cached) {
                return cached;
            }
            const model = this.gemini.getGenerativeModel({
                model: options.model || 'gemini-pro'
            });
            const prompt = this.buildExtractionPrompt(text, options);
            const result = await model.generateContent(prompt);
            const response = result.response;
            const extractionText = response.text();
            const extraction = this.parseExtractionResponse(extractionText, options);
            await (0, redis_1.cacheSet)(cacheKey, extraction, environment_1.config.cache.ttl);
            const duration = Date.now() - startTime;
            (0, logger_1.logAiOperation)('Extract entities', options.model || 'gemini-pro', undefined, duration);
            return extraction;
        }
        catch (error) {
            (0, logger_1.logError)('Failed to extract entities and relationships', error, { options });
            throw new errorHandler_1.ExternalServiceError('Entity extraction failed');
        }
    }
    async generateRecommendations(options) {
        const startTime = Date.now();
        const cacheKey = `ai:recommend:${JSON.stringify(options)}`;
        try {
            const cached = await (0, redis_1.cacheGet)(cacheKey);
            if (cached) {
                return cached;
            }
            const model = this.gemini.getGenerativeModel({ model: 'gemini-pro' });
            const prompt = this.buildRecommendationPrompt(options);
            const result = await model.generateContent(prompt);
            const response = result.response;
            const recommendationText = response.text();
            const recommendations = this.parseRecommendationResponse(recommendationText, options);
            await (0, redis_1.cacheSet)(cacheKey, recommendations, 1800);
            const duration = Date.now() - startTime;
            (0, logger_1.logAiOperation)('Generate recommendations', 'gemini-pro', undefined, duration);
            return recommendations;
        }
        catch (error) {
            (0, logger_1.logError)('Failed to generate recommendations', error, { options });
            throw new errorHandler_1.ExternalServiceError('Recommendation generation failed');
        }
    }
    async checkInteractions(supplements, options) {
        const startTime = Date.now();
        const cacheKey = `ai:interactions:${JSON.stringify(supplements)}:${JSON.stringify(options)}`;
        try {
            const cached = await (0, redis_1.cacheGet)(cacheKey);
            if (cached) {
                return cached;
            }
            const model = this.gemini.getGenerativeModel({ model: 'gemini-pro' });
            const prompt = this.buildInteractionPrompt(supplements, options);
            const result = await model.generateContent(prompt);
            const response = result.response;
            const interactionText = response.text();
            const interactions = this.parseInteractionResponse(interactionText, supplements, options);
            await (0, redis_1.cacheSet)(cacheKey, interactions, environment_1.config.cache.ttl);
            const duration = Date.now() - startTime;
            (0, logger_1.logAiOperation)('Check interactions', 'gemini-pro', undefined, duration);
            return interactions;
        }
        catch (error) {
            (0, logger_1.logError)('Failed to check interactions', error, { supplements, options });
            throw new errorHandler_1.ExternalServiceError('Interaction check failed');
        }
    }
    async summarizeText(text, options) {
        const startTime = Date.now();
        const cacheKey = `ai:summarize:${this.hashString(text)}:${JSON.stringify(options)}`;
        try {
            const cached = await (0, redis_1.cacheGet)(cacheKey);
            if (cached) {
                return cached;
            }
            const model = this.gemini.getGenerativeModel({ model: 'gemini-pro' });
            const prompt = this.buildSummaryPrompt(text, options);
            const result = await model.generateContent(prompt);
            const response = result.response;
            const summaryText = response.text();
            const summary = this.parseSummaryResponse(summaryText, options);
            await (0, redis_1.cacheSet)(cacheKey, summary, environment_1.config.cache.ttl);
            const duration = Date.now() - startTime;
            (0, logger_1.logAiOperation)('Summarize text', 'gemini-pro', undefined, duration);
            return summary;
        }
        catch (error) {
            (0, logger_1.logError)('Failed to summarize text', error, { options });
            throw new errorHandler_1.ExternalServiceError('Text summarization failed');
        }
    }
    async generateQuestions(topic, options) {
        const startTime = Date.now();
        const cacheKey = `ai:questions:${topic}:${JSON.stringify(options)}`;
        try {
            const cached = await (0, redis_1.cacheGet)(cacheKey);
            if (cached) {
                return cached;
            }
            const model = this.gemini.getGenerativeModel({ model: 'gemini-pro' });
            const prompt = this.buildQuestionPrompt(topic, options);
            const result = await model.generateContent(prompt);
            const response = result.response;
            const questionText = response.text();
            const questions = this.parseQuestionResponse(questionText, options);
            await (0, redis_1.cacheSet)(cacheKey, questions, environment_1.config.cache.ttl);
            const duration = Date.now() - startTime;
            (0, logger_1.logAiOperation)('Generate questions', 'gemini-pro', undefined, duration);
            return questions;
        }
        catch (error) {
            (0, logger_1.logError)('Failed to generate questions', error, { topic, options });
            throw new errorHandler_1.ExternalServiceError('Question generation failed');
        }
    }
    async validateClaims(claims, supplement, options) {
        const startTime = Date.now();
        const cacheKey = `ai:validate:${supplement}:${JSON.stringify(claims)}:${JSON.stringify(options)}`;
        try {
            const cached = await (0, redis_1.cacheGet)(cacheKey);
            if (cached) {
                return cached;
            }
            const model = this.gemini.getGenerativeModel({ model: 'gemini-pro' });
            const prompt = this.buildValidationPrompt(claims, supplement, options);
            const result = await model.generateContent(prompt);
            const response = result.response;
            const validationText = response.text();
            const validation = this.parseValidationResponse(validationText, claims, options);
            await (0, redis_1.cacheSet)(cacheKey, validation, environment_1.config.cache.ttl);
            const duration = Date.now() - startTime;
            (0, logger_1.logAiOperation)('Validate claims', 'gemini-pro', undefined, duration);
            return validation;
        }
        catch (error) {
            (0, logger_1.logError)('Failed to validate claims', error, { claims, supplement, options });
            throw new errorHandler_1.ExternalServiceError('Claim validation failed');
        }
    }
    async chat(message, options) {
        const startTime = Date.now();
        try {
            const model = this.gemini.getGenerativeModel({
                model: options.model || 'gemini-pro',
                generationConfig: {
                    temperature: options.temperature || environment_1.config.ai.temperature,
                    maxOutputTokens: environment_1.config.ai.maxTokens,
                },
            });
            const prompt = this.buildChatPrompt(message, options);
            const result = await model.generateContent(prompt);
            const response = result.response;
            const responseText = response.text();
            const chatResponse = {
                message: responseText,
                model: options.model || 'gemini-pro',
                timestamp: new Date().toISOString(),
                context: options.context,
            };
            const duration = Date.now() - startTime;
            (0, logger_1.logAiOperation)('Chat', options.model || 'gemini-pro', undefined, duration);
            return chatResponse;
        }
        catch (error) {
            (0, logger_1.logError)('Failed to process chat message', error, { message, options });
            throw new errorHandler_1.ExternalServiceError('Chat processing failed');
        }
    }
    async getAvailableModels() {
        return {
            gemini: [
                'gemini-pro',
                'gemini-pro-vision',
            ],
            openai: this.openai ? [
                'gpt-4',
                'gpt-4-turbo-preview',
                'gpt-3.5-turbo',
            ] : [],
            default: environment_1.config.ai.defaultModel,
        };
    }
    async getStats() {
        return {
            totalRequests: 0,
            averageResponseTime: 0,
            modelsUsed: await this.getAvailableModels(),
            cacheHitRate: 0,
        };
    }
    async findRelatedConcepts(concept, options) {
        const startTime = Date.now();
        try {
            const model = this.gemini.getGenerativeModel({ model: 'gemini-pro' });
            const prompt = `
        Find ${options.limit || 10} concepts related to "${concept}" in the context of supplements and health.
        
        For each related concept, provide:
        1. Name
        2. Type (Supplement, Ingredient, Effect, Condition, etc.)
        3. Description (brief)
        4. Relationship type to "${concept}"
        5. Confidence score (0-1)
        
        Return as JSON array with this structure:
        [
          {
            "name": "concept name",
            "type": "concept type",
            "description": "brief description",
            "relationshipType": "relationship type",
            "confidence": 0.8
          }
        ]
      `;
            const result = await model.generateContent(prompt);
            const response = result.response;
            const responseText = response.text();
            const concepts = this.parseJsonResponse(responseText);
            const duration = Date.now() - startTime;
            (0, logger_1.logAiOperation)('Find related concepts', 'gemini-pro', undefined, duration);
            return concepts;
        }
        catch (error) {
            (0, logger_1.logError)('Failed to find related concepts', error, { concept, options });
            return [];
        }
    }
    hashString(str) {
        return crypto_1.default.createHash('md5').update(str).digest('hex');
    }
    buildAnalysisPrompt(text, options) {
        const analysisType = options.analysisType || 'general';
        return `
      Analyze the following text in the context of ${analysisType} information:

      "${text}"

      Please provide a comprehensive analysis including:
      1. Key findings and insights
      2. Important entities mentioned (supplements, ingredients, effects, etc.)
      3. Safety considerations or warnings
      4. Scientific evidence level (if applicable)
      5. Practical implications
      ${options.includeConfidence ? '6. Confidence score for each finding (0-1)' : ''}

      Format your response as structured JSON with clear sections.
    `;
    }
    buildExtractionPrompt(text, options) {
        const entityTypes = options.entityTypes || ['Supplement', 'Ingredient', 'Effect', 'Study', 'Interaction'];
        const relationshipTypes = options.relationshipTypes || ['CONTAINS', 'CAUSES', 'INTERACTS_WITH', 'STUDIED_IN'];
        return `
      Extract entities and relationships from the following text:

      "${text}"

      Entity types to look for: ${entityTypes.join(', ')}
      Relationship types to identify: ${relationshipTypes.join(', ')}

      Return a JSON object with this structure:
      {
        "entities": [
          {
            "name": "entity name",
            "type": "entity type",
            "description": "brief description",
            "confidence": 0.8
          }
        ],
        "relationships": [
          {
            "source": "source entity name",
            "target": "target entity name",
            "type": "relationship type",
            "confidence": 0.7
          }
        ]
      }

      Only include entities and relationships with confidence >= ${options.confidence || 0.7}.
    `;
    }
    buildRecommendationPrompt(options) {
        return `
      Generate supplement recommendations based on the following criteria:

      Health conditions: ${options.conditions?.join(', ') || 'None specified'}
      Health goals: ${options.goals?.join(', ') || 'None specified'}
      Current supplements: ${options.currentSupplements?.join(', ') || 'None'}
      Restrictions/allergies: ${options.restrictions?.join(', ') || 'None'}

      Please provide up to ${options.maxRecommendations || 5} recommendations.

      For each recommendation, include:
      1. Supplement name
      2. Reason for recommendation
      3. Suggested dosage
      4. Potential benefits
      5. Safety considerations
      6. Evidence level
      7. Confidence score (0-1)

      Return as JSON array with structured data.

      IMPORTANT: Include appropriate medical disclaimers and advise consulting healthcare providers.
    `;
    }
    buildInteractionPrompt(supplements, options) {
        return `
      Check for potential interactions between these supplements:
      ${supplements.join(', ')}

      ${options.includeFood ? 'Also check for food interactions.' : ''}
      ${options.includeMedications ? 'Also check for common medication interactions.' : ''}

      For each potential interaction, provide:
      1. Supplements involved
      2. Interaction type (synergistic, antagonistic, dangerous, etc.)
      3. Severity level (low, medium, high, critical)
      4. Description of the interaction
      5. Recommendations
      6. Evidence level

      Return as JSON array with structured interaction data.

      IMPORTANT: Include medical disclaimers and emphasize consulting healthcare providers.
    `;
    }
    buildSummaryPrompt(text, options) {
        const summaryType = options.summaryType || 'brief';
        const maxLength = options.maxLength || 500;
        const focusAreas = options.focusAreas || [];
        return `
      Create a ${summaryType} summary of the following text (max ${maxLength} words):

      "${text}"

      ${focusAreas.length > 0 ? `Focus particularly on: ${focusAreas.join(', ')}` : ''}

      Summary style guidelines:
      - Brief: Key points only, very concise
      - Detailed: Comprehensive but accessible
      - Technical: Include scientific details and terminology
      - Consumer: Easy to understand, practical focus

      Return as JSON with:
      {
        "summary": "summary text",
        "keyPoints": ["point 1", "point 2", ...],
        "wordCount": number,
        "summaryType": "${summaryType}"
      }
    `;
    }
    buildQuestionPrompt(topic, options) {
        return `
      Generate ${options.count || 5} ${options.difficulty || 'intermediate'} level questions about: ${topic}

      Question type: ${options.questionType || 'general'}

      Question categories:
      - Research: Questions about studies and evidence
      - Safety: Questions about side effects and contraindications
      - Efficacy: Questions about effectiveness and benefits
      - Dosage: Questions about proper usage and amounts
      - General: Broad questions about the topic

      For each question, provide:
      1. The question text
      2. Question category
      3. Difficulty level
      4. Expected answer type (factual, analytical, opinion, etc.)

      Return as JSON array with structured question data.
    `;
    }
    buildValidationPrompt(claims, supplement, options) {
        return `
      Validate the following claims about ${supplement}:

      Claims to validate:
      ${claims.map((claim, i) => `${i + 1}. ${claim}`).join('\n')}

      Evidence level required: ${options.evidenceLevel || 'medium'}

      For each claim, provide:
      1. Claim text
      2. Validation status (supported, partially supported, not supported, insufficient evidence)
      3. Evidence level (high, medium, low, none)
      4. Supporting evidence summary
      5. Contradicting evidence (if any)
      6. Confidence score (0-1)
      7. Recommendations

      Return as JSON array with structured validation data.

      IMPORTANT: Be objective and evidence-based. Include appropriate disclaimers.
    `;
    }
    buildChatPrompt(message, options) {
        const contextStr = options.context && options.context.length > 0
            ? `\n\nContext from previous conversation:\n${options.context.map(c => `${c.role}: ${c.content}`).join('\n')}`
            : '';
        return `
      You are a knowledgeable assistant specializing in supplements, nutrition, and health.

      User message: ${message}${contextStr}

      Please provide a helpful, accurate, and informative response. Always include appropriate medical disclaimers when discussing health topics.
    `;
    }
    parseAnalysisResponse(text, options) {
        try {
            return this.parseJsonResponse(text);
        }
        catch (error) {
            return {
                analysis: text,
                type: options.analysisType,
                timestamp: new Date().toISOString(),
                includeConfidence: options.includeConfidence,
            };
        }
    }
    parseExtractionResponse(text, _options) {
        try {
            return this.parseJsonResponse(text);
        }
        catch (error) {
            return {
                entities: [],
                relationships: [],
                error: 'Failed to parse extraction response',
                rawResponse: text,
            };
        }
    }
    parseRecommendationResponse(text, _options) {
        try {
            return this.parseJsonResponse(text);
        }
        catch (error) {
            return {
                recommendations: [],
                disclaimer: 'Please consult with a healthcare provider before starting any supplement regimen.',
                error: 'Failed to parse recommendation response',
                rawResponse: text,
            };
        }
    }
    parseInteractionResponse(text, supplements, _options) {
        try {
            return this.parseJsonResponse(text);
        }
        catch (error) {
            return {
                interactions: [],
                supplements,
                disclaimer: 'Please consult with a healthcare provider about potential supplement interactions.',
                error: 'Failed to parse interaction response',
                rawResponse: text,
            };
        }
    }
    parseSummaryResponse(text, options) {
        try {
            return this.parseJsonResponse(text);
        }
        catch (error) {
            return {
                summary: text,
                summaryType: options.summaryType,
                wordCount: text.split(' ').length,
                keyPoints: [],
                error: 'Failed to parse summary response',
            };
        }
    }
    parseQuestionResponse(text, options) {
        try {
            return this.parseJsonResponse(text);
        }
        catch (error) {
            return {
                questions: [],
                topic: 'Unknown',
                questionType: options.questionType,
                difficulty: options.difficulty,
                error: 'Failed to parse question response',
                rawResponse: text,
            };
        }
    }
    parseValidationResponse(text, claims, options) {
        try {
            return this.parseJsonResponse(text);
        }
        catch (error) {
            return {
                validations: [],
                claims,
                evidenceLevel: options.evidenceLevel,
                disclaimer: 'This validation is for informational purposes only. Consult healthcare providers for medical advice.',
                error: 'Failed to parse validation response',
                rawResponse: text,
            };
        }
    }
    parseJsonResponse(text) {
        const jsonMatch = text.match(/\{[\s\S]*\}|\[[\s\S]*\]/);
        if (jsonMatch) {
            try {
                return JSON.parse(jsonMatch[0]);
            }
            catch (error) {
                throw new Error('Invalid JSON in response');
            }
        }
        try {
            return JSON.parse(text);
        }
        catch (error) {
            throw new Error('No valid JSON found in response');
        }
    }
}
exports.AIService = AIService;
//# sourceMappingURL=AIService.js.map