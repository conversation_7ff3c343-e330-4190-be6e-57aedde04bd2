"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UploadService = void 0;
const promises_1 = __importDefault(require("fs/promises"));
const path_1 = __importDefault(require("path"));
const mongodb_1 = require("@/config/mongodb");
const RAGService_1 = require("@/services/RAGService");
const AIService_1 = require("@/services/AIService");
const environment_1 = require("@/config/environment");
const logger_1 = require("@/utils/logger");
const errorHandler_1 = require("@/middleware/errorHandler");
const uuid_1 = require("uuid");
const pdf_parse_1 = __importDefault(require("pdf-parse"));
const mammoth_1 = __importDefault(require("mammoth"));
const axios_1 = __importDefault(require("axios"));
const fileSchema = new mongodb_1.mongoose.Schema({
    id: { type: String, unique: true, required: true },
    originalName: { type: String, required: true },
    fileName: { type: String, required: true },
    filePath: { type: String, required: true },
    mimeType: { type: String, required: true },
    size: { type: Number, required: true },
    extractedText: { type: String },
    metadata: { type: mongodb_1.mongoose.Schema.Types.Mixed },
    source: { type: String },
    processed: { type: Boolean, default: false },
    addedToKnowledgeBase: { type: Boolean, default: false },
    createdAt: { type: Date, default: Date.now },
    updatedAt: { type: Date, default: Date.now },
});
const FileModel = mongodb_1.mongoose.model('UploadedFile', fileSchema);
class UploadService {
    ragService;
    _aiService;
    constructor() {
        this.ragService = new RAGService_1.RAGService();
        this._aiService = new AIService_1.AIService();
    }
    async processFile(file, options) {
        const startTime = Date.now();
        try {
            const fileId = (0, uuid_1.v4)();
            let extractedText = '';
            if (options.extractText) {
                extractedText = await this.extractTextFromFile(file);
            }
            const fileDoc = new FileModel({
                id: fileId,
                originalName: file.originalname,
                fileName: file.filename,
                filePath: file.path,
                mimeType: file.mimetype,
                size: file.size,
                extractedText,
                metadata: options.metadata || {},
                source: options.source,
                processed: true,
                addedToKnowledgeBase: false,
            });
            await fileDoc.save();
            if (options.addToKnowledgeBase && extractedText) {
                await this.addToKnowledgeBase(fileId, extractedText, {
                    title: file.originalname,
                    source: options.source || 'file_upload',
                    fileName: file.filename,
                    mimeType: file.mimetype,
                    ...options.metadata,
                });
                fileDoc.addedToKnowledgeBase = true;
                await fileDoc.save();
            }
            const duration = Date.now() - startTime;
            (0, logger_1.logDatabaseOperation)('Process file', 'Upload', duration, {
                fileName: file.originalname,
                size: file.size,
                extractedText: !!extractedText,
                addedToKnowledgeBase: options.addToKnowledgeBase && !!extractedText,
            });
            return {
                id: fileId,
                originalName: file.originalname,
                fileName: file.filename,
                mimeType: file.mimetype,
                size: file.size,
                extractedText: extractedText ? extractedText.substring(0, 500) + '...' : null,
                addedToKnowledgeBase: fileDoc.addedToKnowledgeBase,
                timestamp: new Date().toISOString(),
            };
        }
        catch (error) {
            (0, logger_1.logError)('Failed to process file', error, { fileName: file.originalname, options });
            throw new errorHandler_1.DatabaseError('File processing failed');
        }
    }
    async processFiles(files, options) {
        const startTime = Date.now();
        try {
            const results = [];
            const errors = [];
            for (const file of files) {
                try {
                    const result = await this.processFile(file, options);
                    results.push(result);
                }
                catch (error) {
                    errors.push({
                        fileName: file.originalname,
                        error: error.message,
                    });
                }
            }
            const duration = Date.now() - startTime;
            (0, logger_1.logDatabaseOperation)('Process files', 'Upload', duration, {
                totalFiles: files.length,
                successful: results.length,
                errors: errors.length,
            });
            return {
                successful: results,
                errors,
                totalFiles: files.length,
                successCount: results.length,
                errorCount: errors.length,
                timestamp: new Date().toISOString(),
            };
        }
        catch (error) {
            (0, logger_1.logError)('Failed to process files', error, { fileCount: files.length, options });
            throw new errorHandler_1.DatabaseError('Batch file processing failed');
        }
    }
    async processUrl(url, options) {
        const startTime = Date.now();
        try {
            const response = await axios_1.default.get(url, {
                timeout: 30000,
                maxContentLength: environment_1.config.upload.maxFileSize,
            });
            const content = response.data;
            const contentType = response.headers['content-type'] || 'text/plain';
            let extractedText = '';
            if (options.extractText) {
                if (contentType.includes('text/') || contentType.includes('application/json')) {
                    extractedText = typeof content === 'string' ? content : JSON.stringify(content);
                }
                else {
                    throw new errorHandler_1.ValidationError('Cannot extract text from this content type');
                }
            }
            const fileId = (0, uuid_1.v4)();
            const fileName = path_1.default.basename(new URL(url).pathname) || 'downloaded_content';
            const fileDoc = new FileModel({
                id: fileId,
                originalName: fileName,
                fileName: fileName,
                filePath: url,
                mimeType: contentType,
                size: Buffer.byteLength(extractedText || content),
                extractedText,
                metadata: { url, ...options.metadata },
                source: options.source || 'url_download',
                processed: true,
                addedToKnowledgeBase: false,
            });
            await fileDoc.save();
            if (options.addToKnowledgeBase && extractedText) {
                await this.addToKnowledgeBase(fileId, extractedText, {
                    title: fileName,
                    source: options.source || 'url_download',
                    url,
                    ...options.metadata,
                });
                fileDoc.addedToKnowledgeBase = true;
                await fileDoc.save();
            }
            const duration = Date.now() - startTime;
            (0, logger_1.logDatabaseOperation)('Process URL', 'Upload', duration, {
                url,
                contentType,
                size: fileDoc.size,
                extractedText: !!extractedText,
            });
            return {
                id: fileId,
                originalName: fileName,
                url,
                mimeType: contentType,
                size: fileDoc.size,
                extractedText: extractedText ? extractedText.substring(0, 500) + '...' : null,
                addedToKnowledgeBase: fileDoc.addedToKnowledgeBase,
                timestamp: new Date().toISOString(),
            };
        }
        catch (error) {
            (0, logger_1.logError)('Failed to process URL', error, { url, options });
            throw new errorHandler_1.DatabaseError('URL processing failed');
        }
    }
    async getFile(id) {
        const startTime = Date.now();
        try {
            const file = await FileModel.findOne({ id });
            if (!file) {
                throw new errorHandler_1.NotFoundError(`File with id ${id} not found`);
            }
            const duration = Date.now() - startTime;
            (0, logger_1.logDatabaseOperation)('Get file', 'Upload', duration, { id });
            return {
                id: file.id,
                originalName: file.originalName,
                fileName: file.fileName,
                filePath: file.filePath,
                mimeType: file.mimeType,
                size: file.size,
                metadata: file.metadata,
                source: file.source,
                processed: file.processed,
                addedToKnowledgeBase: file.addedToKnowledgeBase,
                createdAt: file.createdAt,
                updatedAt: file.updatedAt,
            };
        }
        catch (error) {
            if (error instanceof errorHandler_1.NotFoundError)
                throw error;
            (0, logger_1.logError)('Failed to get file', error, { id });
            throw new errorHandler_1.DatabaseError('File retrieval failed');
        }
    }
    async getFileContent(id) {
        const startTime = Date.now();
        try {
            const file = await FileModel.findOne({ id });
            if (!file) {
                throw new errorHandler_1.NotFoundError(`File with id ${id} not found`);
            }
            const duration = Date.now() - startTime;
            (0, logger_1.logDatabaseOperation)('Get file content', 'Upload', duration, { id });
            return {
                id: file.id,
                originalName: file.originalName,
                extractedText: file.extractedText,
                mimeType: file.mimeType,
                size: file.size,
                timestamp: new Date().toISOString(),
            };
        }
        catch (error) {
            if (error instanceof errorHandler_1.NotFoundError)
                throw error;
            (0, logger_1.logError)('Failed to get file content', error, { id });
            throw new errorHandler_1.DatabaseError('File content retrieval failed');
        }
    }
    async fileExists(filePath) {
        try {
            await promises_1.default.access(filePath);
            return true;
        }
        catch {
            return false;
        }
    }
    async listFiles(options) {
        const startTime = Date.now();
        try {
            const page = options.page || 1;
            const limit = Math.min(options.limit || 20, 100);
            const skip = (page - 1) * limit;
            const query = {};
            if (options.type) {
                query.mimeType = { $regex: options.type, $options: 'i' };
            }
            if (options.source) {
                query.source = options.source;
            }
            if (options.search) {
                query.$or = [
                    { originalName: { $regex: options.search, $options: 'i' } },
                    { extractedText: { $regex: options.search, $options: 'i' } },
                ];
            }
            const [files, totalCount] = await Promise.all([
                FileModel.find(query)
                    .sort({ createdAt: -1 })
                    .skip(skip)
                    .limit(limit)
                    .lean(),
                FileModel.countDocuments(query),
            ]);
            const duration = Date.now() - startTime;
            (0, logger_1.logDatabaseOperation)('List files', 'Upload', duration, {
                resultCount: files.length,
                totalCount,
                page,
                limit,
            });
            return {
                files: files.map(file => ({
                    id: file.id,
                    originalName: file.originalName,
                    fileName: file.fileName,
                    mimeType: file.mimeType,
                    size: file.size,
                    source: file.source,
                    processed: file.processed,
                    addedToKnowledgeBase: file.addedToKnowledgeBase,
                    createdAt: file.createdAt,
                    updatedAt: file.updatedAt,
                })),
                pagination: {
                    page,
                    limit,
                    totalCount,
                    totalPages: Math.ceil(totalCount / limit),
                    hasNext: page * limit < totalCount,
                    hasPrev: page > 1,
                },
                timestamp: new Date().toISOString(),
            };
        }
        catch (error) {
            (0, logger_1.logError)('Failed to list files', error, { options });
            throw new errorHandler_1.DatabaseError('File listing failed');
        }
    }
    async deleteFile(id) {
        const startTime = Date.now();
        try {
            const file = await FileModel.findOne({ id });
            if (!file) {
                throw new errorHandler_1.NotFoundError(`File with id ${id} not found`);
            }
            if (!file.filePath.startsWith('http') && await this.fileExists(file.filePath)) {
                await promises_1.default.unlink(file.filePath);
            }
            await FileModel.deleteOne({ id });
            const duration = Date.now() - startTime;
            (0, logger_1.logDatabaseOperation)('Delete file', 'Upload', duration, { id });
        }
        catch (error) {
            if (error instanceof errorHandler_1.NotFoundError)
                throw error;
            (0, logger_1.logError)('Failed to delete file', error, { id });
            throw new errorHandler_1.DatabaseError('File deletion failed');
        }
    }
    async batchProcess(fileIds, operation, options) {
        const startTime = Date.now();
        try {
            const results = [];
            const errors = [];
            for (const fileId of fileIds) {
                try {
                    let result;
                    switch (operation) {
                        case 'addToKnowledgeBase':
                            result = await this.addFileToKnowledgeBase(fileId, options);
                            break;
                        case 'extractText':
                            result = await this.extractTextFromFileId(fileId);
                            break;
                        case 'delete':
                            await this.deleteFile(fileId);
                            result = { id: fileId, deleted: true };
                            break;
                        default:
                            throw new errorHandler_1.ValidationError(`Unknown operation: ${operation}`);
                    }
                    results.push(result);
                }
                catch (error) {
                    errors.push({
                        fileId,
                        error: error.message,
                    });
                }
            }
            const duration = Date.now() - startTime;
            (0, logger_1.logDatabaseOperation)('Batch process', 'Upload', duration, {
                operation,
                totalFiles: fileIds.length,
                successful: results.length,
                errors: errors.length,
            });
            return {
                operation,
                successful: results,
                errors,
                totalFiles: fileIds.length,
                successCount: results.length,
                errorCount: errors.length,
                timestamp: new Date().toISOString(),
            };
        }
        catch (error) {
            (0, logger_1.logError)('Failed to batch process files', error, { fileIds, operation, options });
            throw new errorHandler_1.DatabaseError('Batch processing failed');
        }
    }
    async getStats() {
        const startTime = Date.now();
        try {
            const [totalFiles, processedFiles, knowledgeBaseFiles, sizeStats, typeStats,] = await Promise.all([
                FileModel.countDocuments(),
                FileModel.countDocuments({ processed: true }),
                FileModel.countDocuments({ addedToKnowledgeBase: true }),
                FileModel.aggregate([
                    {
                        $group: {
                            _id: null,
                            totalSize: { $sum: '$size' },
                            avgSize: { $avg: '$size' },
                            maxSize: { $max: '$size' },
                            minSize: { $min: '$size' },
                        },
                    },
                ]),
                FileModel.aggregate([
                    {
                        $group: {
                            _id: '$mimeType',
                            count: { $sum: 1 },
                            totalSize: { $sum: '$size' },
                        },
                    },
                    { $sort: { count: -1 } },
                ]),
            ]);
            const duration = Date.now() - startTime;
            (0, logger_1.logDatabaseOperation)('Get upload stats', 'Upload', duration);
            return {
                totalFiles,
                processedFiles,
                knowledgeBaseFiles,
                processingRate: totalFiles > 0 ? (processedFiles / totalFiles) * 100 : 0,
                knowledgeBaseRate: totalFiles > 0 ? (knowledgeBaseFiles / totalFiles) * 100 : 0,
                sizeStats: sizeStats[0] || {
                    totalSize: 0,
                    avgSize: 0,
                    maxSize: 0,
                    minSize: 0,
                },
                typeStats,
                timestamp: new Date().toISOString(),
            };
        }
        catch (error) {
            (0, logger_1.logError)('Failed to get upload stats', error);
            throw new errorHandler_1.DatabaseError('Failed to get upload statistics');
        }
    }
    async extractTextFromFile(file) {
        try {
            const buffer = await promises_1.default.readFile(file.path);
            switch (file.mimetype) {
                case 'application/pdf':
                    const pdfData = await (0, pdf_parse_1.default)(buffer);
                    return pdfData.text;
                case 'application/msword':
                case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
                    const docData = await mammoth_1.default.extractRawText({ buffer });
                    return docData.value;
                case 'text/plain':
                case 'text/csv':
                case 'application/json':
                case 'application/xml':
                case 'text/xml':
                    return buffer.toString('utf-8');
                default:
                    throw new errorHandler_1.ValidationError(`Text extraction not supported for ${file.mimetype}`);
            }
        }
        catch (error) {
            (0, logger_1.logError)('Failed to extract text from file', error, {
                fileName: file.originalname,
                mimeType: file.mimetype,
            });
            throw new errorHandler_1.DatabaseError('Text extraction failed');
        }
    }
    async extractTextFromFileId(fileId) {
        const file = await FileModel.findOne({ id: fileId });
        if (!file) {
            throw new errorHandler_1.NotFoundError(`File with id ${fileId} not found`);
        }
        if (file.extractedText) {
            return {
                id: fileId,
                extractedText: file.extractedText,
                alreadyExtracted: true,
            };
        }
        if (file.filePath.startsWith('http')) {
            throw new errorHandler_1.ValidationError('Cannot re-extract text from URL-based files');
        }
        if (!await this.fileExists(file.filePath)) {
            throw new errorHandler_1.NotFoundError('Physical file not found');
        }
        const mockFile = {
            path: file.filePath,
            originalname: file.originalName,
            mimetype: file.mimeType,
        };
        const extractedText = await this.extractTextFromFile(mockFile);
        file.extractedText = extractedText;
        file.updatedAt = new Date();
        await file.save();
        return {
            id: fileId,
            extractedText,
            alreadyExtracted: false,
        };
    }
    async addToKnowledgeBase(fileId, text, metadata) {
        try {
            await this.ragService.addDocument({
                content: text,
                title: metadata.title,
                source: metadata.source,
                metadata: {
                    fileId,
                    ...metadata,
                },
                className: 'Document',
            });
        }
        catch (error) {
            (0, logger_1.logError)('Failed to add file to knowledge base', error, { fileId, metadata });
            throw new errorHandler_1.DatabaseError('Knowledge base addition failed');
        }
    }
    async addFileToKnowledgeBase(fileId, options) {
        const file = await FileModel.findOne({ id: fileId });
        if (!file) {
            throw new errorHandler_1.NotFoundError(`File with id ${fileId} not found`);
        }
        if (file.addedToKnowledgeBase) {
            return {
                id: fileId,
                addedToKnowledgeBase: true,
                alreadyAdded: true,
            };
        }
        if (!file.extractedText) {
            throw new errorHandler_1.ValidationError('File has no extracted text to add to knowledge base');
        }
        await this.addToKnowledgeBase(fileId, file.extractedText, {
            title: file.originalName,
            source: file.source || 'file_upload',
            fileName: file.fileName,
            mimeType: file.mimeType,
            ...file.metadata,
            ...options,
        });
        file.addedToKnowledgeBase = true;
        file.updatedAt = new Date();
        await file.save();
        return {
            id: fileId,
            addedToKnowledgeBase: true,
            alreadyAdded: false,
        };
    }
}
exports.UploadService = UploadService;
//# sourceMappingURL=UploadService.js.map