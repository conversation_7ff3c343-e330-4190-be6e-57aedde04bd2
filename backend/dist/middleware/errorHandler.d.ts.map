{"version": 3, "file": "errorHandler.d.ts", "sourceRoot": "", "sources": ["../../src/middleware/errorHandler.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,MAAM,SAAS,CAAC;AAK1D,qBAAa,QAAS,SAAQ,KAAK;IAC1B,UAAU,EAAE,MAAM,CAAC;IACnB,aAAa,EAAE,OAAO,CAAC;IACvB,IAAI,CAAC,EAAE,MAAM,CAAC;gBAET,OAAO,EAAE,MAAM,EAAE,UAAU,GAAE,MAAY,EAAE,aAAa,GAAE,OAAc;CAOrF;AAGD,qBAAa,eAAgB,SAAQ,QAAQ;gBAC/B,OAAO,EAAE,MAAM;CAI5B;AAED,qBAAa,mBAAoB,SAAQ,QAAQ;gBACnC,OAAO,GAAE,MAAkC;CAIxD;AAED,qBAAa,kBAAmB,SAAQ,QAAQ;gBAClC,OAAO,GAAE,MAAmC;CAIzD;AAED,qBAAa,aAAc,SAAQ,QAAQ;gBAC7B,OAAO,GAAE,MAA6B;CAInD;AAED,qBAAa,aAAc,SAAQ,QAAQ;gBAC7B,OAAO,GAAE,MAA4B;CAIlD;AAED,qBAAa,cAAe,SAAQ,QAAQ;gBAC9B,OAAO,GAAE,MAA4B;CAIlD;AAED,qBAAa,aAAc,SAAQ,QAAQ;gBAC7B,OAAO,GAAE,MAAoC;CAI1D;AAED,qBAAa,oBAAqB,SAAQ,QAAQ;gBACpC,OAAO,GAAE,MAAiC;CAIvD;AA+ID,eAAO,MAAM,YAAY,GACvB,KAAK,GAAG,EACR,KAAK,OAAO,EACZ,KAAK,QAAQ,EACb,OAAO,YAAY,KAClB,IAmDF,CAAC;AAGF,eAAO,MAAM,UAAU,GAAI,IAAI,QAAQ,MAC7B,KAAK,OAAO,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,SAGxD,CAAC;AAGF,eAAO,MAAM,eAAe,GAAI,KAAK,OAAO,EAAE,MAAM,QAAQ,EAAE,MAAM,YAAY,KAAG,IAGlF,CAAC;AAEF,eAAe,YAAY,CAAC"}