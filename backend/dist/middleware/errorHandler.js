"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.notFoundHandler = exports.catchAsync = exports.errorHandler = exports.ExternalServiceError = exports.DatabaseError = exports.RateLimitError = exports.ConflictError = exports.NotFoundError = exports.AuthorizationError = exports.AuthenticationError = exports.ValidationError = exports.AppError = void 0;
const environment_1 = require("@/config/environment");
const logger_1 = require("@/utils/logger");
class AppError extends Error {
    statusCode;
    isOperational;
    code;
    constructor(message, statusCode = 500, isOperational = true) {
        super(message);
        this.statusCode = statusCode;
        this.isOperational = isOperational;
        Error.captureStackTrace(this, this.constructor);
    }
}
exports.AppError = AppError;
class ValidationError extends AppError {
    constructor(message) {
        super(message, 400);
        this.code = 'VALIDATION_ERROR';
    }
}
exports.ValidationError = ValidationError;
class AuthenticationError extends AppError {
    constructor(message = 'Authentication required') {
        super(message, 401);
        this.code = 'AUTHENTICATION_ERROR';
    }
}
exports.AuthenticationError = AuthenticationError;
class AuthorizationError extends AppError {
    constructor(message = 'Insufficient permissions') {
        super(message, 403);
        this.code = 'AUTHORIZATION_ERROR';
    }
}
exports.AuthorizationError = AuthorizationError;
class NotFoundError extends AppError {
    constructor(message = 'Resource not found') {
        super(message, 404);
        this.code = 'NOT_FOUND_ERROR';
    }
}
exports.NotFoundError = NotFoundError;
class ConflictError extends AppError {
    constructor(message = 'Resource conflict') {
        super(message, 409);
        this.code = 'CONFLICT_ERROR';
    }
}
exports.ConflictError = ConflictError;
class RateLimitError extends AppError {
    constructor(message = 'Too many requests') {
        super(message, 429);
        this.code = 'RATE_LIMIT_ERROR';
    }
}
exports.RateLimitError = RateLimitError;
class DatabaseError extends AppError {
    constructor(message = 'Database operation failed') {
        super(message, 500);
        this.code = 'DATABASE_ERROR';
    }
}
exports.DatabaseError = DatabaseError;
class ExternalServiceError extends AppError {
    constructor(message = 'External service error') {
        super(message, 502);
        this.code = 'EXTERNAL_SERVICE_ERROR';
    }
}
exports.ExternalServiceError = ExternalServiceError;
const handleCastErrorDB = (err) => {
    const message = `Invalid ${err.path}: ${err.value}`;
    return new ValidationError(message);
};
const handleDuplicateFieldsDB = (err) => {
    const value = err.errmsg?.match(/(["'])(\\?.)*?\1/)?.[0];
    const message = `Duplicate field value: ${value}. Please use another value!`;
    return new ConflictError(message);
};
const handleValidationErrorDB = (err) => {
    const errors = Object.values(err.errors).map((el) => el.message);
    const message = `Invalid input data. ${errors.join('. ')}`;
    return new ValidationError(message);
};
const handleJWTError = () => {
    return new AuthenticationError('Invalid token. Please log in again!');
};
const handleJWTExpiredError = () => {
    return new AuthenticationError('Your token has expired! Please log in again.');
};
const handleNeo4jError = (err) => {
    if (err.code === 'Neo.ClientError.Statement.SyntaxError') {
        return new ValidationError('Invalid query syntax');
    }
    if (err.code === 'Neo.TransientError.General.DatabaseUnavailable') {
        return new DatabaseError('Database temporarily unavailable');
    }
    return new DatabaseError('Database operation failed');
};
const handleWeaviateError = (err) => {
    if (err.message?.includes('unauthorized')) {
        return new AuthenticationError('Vector database authentication failed');
    }
    if (err.message?.includes('not found')) {
        return new NotFoundError('Vector database resource not found');
    }
    return new ExternalServiceError('Vector database operation failed');
};
const handleRedisError = (err) => {
    if (err.code === 'ECONNREFUSED') {
        return new ExternalServiceError('Cache service unavailable');
    }
    return new ExternalServiceError('Cache operation failed');
};
const sendErrorDev = (err, req, res) => {
    const errorObj = {
        message: err.message,
        statusCode: err.statusCode,
        timestamp: new Date().toISOString(),
        path: req.originalUrl,
        method: req.method,
        details: err,
    };
    if (err.code) {
        errorObj.code = err.code;
    }
    if (err.stack) {
        errorObj.stack = err.stack;
    }
    const errorResponse = {
        success: false,
        error: errorObj,
    };
    res.status(err.statusCode).json(errorResponse);
};
const sendErrorProd = (err, req, res) => {
    if (err.isOperational) {
        const errorObj = {
            message: err.message,
            statusCode: err.statusCode,
            timestamp: new Date().toISOString(),
            path: req.originalUrl,
            method: req.method,
        };
        if (err.code) {
            errorObj.code = err.code;
        }
        const errorResponse = {
            success: false,
            error: errorObj,
        };
        res.status(err.statusCode).json(errorResponse);
    }
    else {
        (0, logger_1.logError)('Unknown error occurred', err, {
            url: req.originalUrl,
            method: req.method,
            ip: req.ip,
            userAgent: req.get('User-Agent'),
        });
        const errorResponse = {
            success: false,
            error: {
                message: 'Something went wrong!',
                statusCode: 500,
                timestamp: new Date().toISOString(),
                path: req.originalUrl,
                method: req.method,
            },
        };
        res.status(500).json(errorResponse);
    }
};
const errorHandler = (err, req, res, _next) => {
    let error = { ...err };
    error.message = err.message;
    const errorContext = {
        url: req.originalUrl,
        method: req.method,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        userId: req.user?.id,
    };
    if (err.statusCode === 401 || err.statusCode === 403) {
        (0, logger_1.logSecurityEvent)(`${err.statusCode === 401 ? 'Authentication' : 'Authorization'} failure`, 'medium', errorContext);
    }
    if (err.statusCode === 429) {
        (0, logger_1.logSecurityEvent)('Rate limit exceeded', 'low', errorContext);
    }
    if (err.name === 'CastError')
        error = handleCastErrorDB(err);
    if (err.code === 11000)
        error = handleDuplicateFieldsDB(err);
    if (err.name === 'ValidationError')
        error = handleValidationErrorDB(err);
    if (err.name === 'JsonWebTokenError')
        error = handleJWTError();
    if (err.name === 'TokenExpiredError')
        error = handleJWTExpiredError();
    if (err.code?.startsWith('Neo.'))
        error = handleNeo4jError(err);
    if (err.name === 'WeaviateError')
        error = handleWeaviateError(err);
    if (err.code === 'ECONNREFUSED' && err.syscall === 'connect') {
        error = handleRedisError(err);
    }
    if (!(error instanceof AppError)) {
        error = new AppError(error.message || 'Something went wrong!', error.statusCode || 500);
    }
    if (environment_1.config.isDevelopment) {
        sendErrorDev(error, req, res);
    }
    else {
        sendErrorProd(error, req, res);
    }
};
exports.errorHandler = errorHandler;
const catchAsync = (fn) => {
    return (req, res, next) => {
        fn(req, res, next).catch(next);
    };
};
exports.catchAsync = catchAsync;
const notFoundHandler = (req, _res, next) => {
    const err = new NotFoundError(`Can't find ${req.originalUrl} on this server!`);
    next(err);
};
exports.notFoundHandler = notFoundHandler;
exports.default = exports.errorHandler;
//# sourceMappingURL=errorHandler.js.map