{"version": 3, "file": "errorHandler.js", "sourceRoot": "", "sources": ["../../src/middleware/errorHandler.ts"], "names": [], "mappings": ";;;AACA,sDAA8C;AAC9C,2CAA4D;AAG5D,MAAa,QAAS,SAAQ,KAAK;IAC1B,UAAU,CAAS;IACnB,aAAa,CAAU;IACvB,IAAI,CAAU;IAErB,YAAY,OAAe,EAAE,aAAqB,GAAG,EAAE,gBAAyB,IAAI;QAClF,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QAEnC,KAAK,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;IAClD,CAAC;CACF;AAZD,4BAYC;AAGD,MAAa,eAAgB,SAAQ,QAAQ;IAC3C,YAAY,OAAe;QACzB,KAAK,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;QACpB,IAAI,CAAC,IAAI,GAAG,kBAAkB,CAAC;IACjC,CAAC;CACF;AALD,0CAKC;AAED,MAAa,mBAAoB,SAAQ,QAAQ;IAC/C,YAAY,UAAkB,yBAAyB;QACrD,KAAK,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;QACpB,IAAI,CAAC,IAAI,GAAG,sBAAsB,CAAC;IACrC,CAAC;CACF;AALD,kDAKC;AAED,MAAa,kBAAmB,SAAQ,QAAQ;IAC9C,YAAY,UAAkB,0BAA0B;QACtD,KAAK,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;QACpB,IAAI,CAAC,IAAI,GAAG,qBAAqB,CAAC;IACpC,CAAC;CACF;AALD,gDAKC;AAED,MAAa,aAAc,SAAQ,QAAQ;IACzC,YAAY,UAAkB,oBAAoB;QAChD,KAAK,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;QACpB,IAAI,CAAC,IAAI,GAAG,iBAAiB,CAAC;IAChC,CAAC;CACF;AALD,sCAKC;AAED,MAAa,aAAc,SAAQ,QAAQ;IACzC,YAAY,UAAkB,mBAAmB;QAC/C,KAAK,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;QACpB,IAAI,CAAC,IAAI,GAAG,gBAAgB,CAAC;IAC/B,CAAC;CACF;AALD,sCAKC;AAED,MAAa,cAAe,SAAQ,QAAQ;IAC1C,YAAY,UAAkB,mBAAmB;QAC/C,KAAK,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;QACpB,IAAI,CAAC,IAAI,GAAG,kBAAkB,CAAC;IACjC,CAAC;CACF;AALD,wCAKC;AAED,MAAa,aAAc,SAAQ,QAAQ;IACzC,YAAY,UAAkB,2BAA2B;QACvD,KAAK,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;QACpB,IAAI,CAAC,IAAI,GAAG,gBAAgB,CAAC;IAC/B,CAAC;CACF;AALD,sCAKC;AAED,MAAa,oBAAqB,SAAQ,QAAQ;IAChD,YAAY,UAAkB,wBAAwB;QACpD,KAAK,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;QACpB,IAAI,CAAC,IAAI,GAAG,wBAAwB,CAAC;IACvC,CAAC;CACF;AALD,oDAKC;AAkBD,MAAM,iBAAiB,GAAG,CAAC,GAAQ,EAAY,EAAE;IAC/C,MAAM,OAAO,GAAG,WAAW,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,KAAK,EAAE,CAAC;IACpD,OAAO,IAAI,eAAe,CAAC,OAAO,CAAC,CAAC;AACtC,CAAC,CAAC;AAEF,MAAM,uBAAuB,GAAG,CAAC,GAAQ,EAAY,EAAE;IACrD,MAAM,KAAK,GAAG,GAAG,CAAC,MAAM,EAAE,KAAK,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACzD,MAAM,OAAO,GAAG,0BAA0B,KAAK,6BAA6B,CAAC;IAC7E,OAAO,IAAI,aAAa,CAAC,OAAO,CAAC,CAAC;AACpC,CAAC,CAAC;AAEF,MAAM,uBAAuB,GAAG,CAAC,GAAQ,EAAY,EAAE;IACrD,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,EAAO,EAAE,EAAE,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC;IACtE,MAAM,OAAO,GAAG,uBAAuB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;IAC3D,OAAO,IAAI,eAAe,CAAC,OAAO,CAAC,CAAC;AACtC,CAAC,CAAC;AAEF,MAAM,cAAc,GAAG,GAAa,EAAE;IACpC,OAAO,IAAI,mBAAmB,CAAC,qCAAqC,CAAC,CAAC;AACxE,CAAC,CAAC;AAEF,MAAM,qBAAqB,GAAG,GAAa,EAAE;IAC3C,OAAO,IAAI,mBAAmB,CAAC,8CAA8C,CAAC,CAAC;AACjF,CAAC,CAAC;AAEF,MAAM,gBAAgB,GAAG,CAAC,GAAQ,EAAY,EAAE;IAC9C,IAAI,GAAG,CAAC,IAAI,KAAK,uCAAuC,EAAE,CAAC;QACzD,OAAO,IAAI,eAAe,CAAC,sBAAsB,CAAC,CAAC;IACrD,CAAC;IACD,IAAI,GAAG,CAAC,IAAI,KAAK,gDAAgD,EAAE,CAAC;QAClE,OAAO,IAAI,aAAa,CAAC,kCAAkC,CAAC,CAAC;IAC/D,CAAC;IACD,OAAO,IAAI,aAAa,CAAC,2BAA2B,CAAC,CAAC;AACxD,CAAC,CAAC;AAEF,MAAM,mBAAmB,GAAG,CAAC,GAAQ,EAAY,EAAE;IACjD,IAAI,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;QAC1C,OAAO,IAAI,mBAAmB,CAAC,uCAAuC,CAAC,CAAC;IAC1E,CAAC;IACD,IAAI,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;QACvC,OAAO,IAAI,aAAa,CAAC,oCAAoC,CAAC,CAAC;IACjE,CAAC;IACD,OAAO,IAAI,oBAAoB,CAAC,kCAAkC,CAAC,CAAC;AACtE,CAAC,CAAC;AAEF,MAAM,gBAAgB,GAAG,CAAC,GAAQ,EAAY,EAAE;IAC9C,IAAI,GAAG,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;QAChC,OAAO,IAAI,oBAAoB,CAAC,2BAA2B,CAAC,CAAC;IAC/D,CAAC;IACD,OAAO,IAAI,oBAAoB,CAAC,wBAAwB,CAAC,CAAC;AAC5D,CAAC,CAAC;AAGF,MAAM,YAAY,GAAG,CAAC,GAAa,EAAE,GAAY,EAAE,GAAa,EAAQ,EAAE;IACxE,MAAM,QAAQ,GAAQ;QACpB,OAAO,EAAE,GAAG,CAAC,OAAO;QACpB,UAAU,EAAE,GAAG,CAAC,UAAU;QAC1B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;QACnC,IAAI,EAAE,GAAG,CAAC,WAAW;QACrB,MAAM,EAAE,GAAG,CAAC,MAAM;QAClB,OAAO,EAAE,GAAG;KACb,CAAC;IAEF,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC;QACb,QAAQ,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC;IAC3B,CAAC;IAED,IAAI,GAAG,CAAC,KAAK,EAAE,CAAC;QACd,QAAQ,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC;IAC7B,CAAC;IAED,MAAM,aAAa,GAAkB;QACnC,OAAO,EAAE,KAAK;QACd,KAAK,EAAE,QAAQ;KAChB,CAAC;IAEF,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AACjD,CAAC,CAAC;AAEF,MAAM,aAAa,GAAG,CAAC,GAAa,EAAE,GAAY,EAAE,GAAa,EAAQ,EAAE;IAEzE,IAAI,GAAG,CAAC,aAAa,EAAE,CAAC;QACtB,MAAM,QAAQ,GAAQ;YACpB,OAAO,EAAE,GAAG,CAAC,OAAO;YACpB,UAAU,EAAE,GAAG,CAAC,UAAU;YAC1B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,IAAI,EAAE,GAAG,CAAC,WAAW;YACrB,MAAM,EAAE,GAAG,CAAC,MAAM;SACnB,CAAC;QAEF,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC;YACb,QAAQ,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC;QAC3B,CAAC;QAED,MAAM,aAAa,GAAkB;YACnC,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,QAAQ;SAChB,CAAC;QAEF,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IACjD,CAAC;SAAM,CAAC;QAEN,IAAA,iBAAQ,EAAC,wBAAwB,EAAE,GAAG,EAAE;YACtC,GAAG,EAAE,GAAG,CAAC,WAAW;YACpB,MAAM,EAAE,GAAG,CAAC,MAAM;YAClB,EAAE,EAAE,GAAG,CAAC,EAAE;YACV,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;SACjC,CAAC,CAAC;QAEH,MAAM,aAAa,GAAkB;YACnC,OAAO,EAAE,KAAK;YACd,KAAK,EAAE;gBACL,OAAO,EAAE,uBAAuB;gBAChC,UAAU,EAAE,GAAG;gBACf,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,IAAI,EAAE,GAAG,CAAC,WAAW;gBACrB,MAAM,EAAE,GAAG,CAAC,MAAM;aACnB;SACF,CAAC;QAEF,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IACtC,CAAC;AACH,CAAC,CAAC;AAGK,MAAM,YAAY,GAAG,CAC1B,GAAQ,EACR,GAAY,EACZ,GAAa,EACb,KAAmB,EACb,EAAE;IACR,IAAI,KAAK,GAAG,EAAE,GAAG,GAAG,EAAE,CAAC;IACvB,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC;IAG5B,MAAM,YAAY,GAAG;QACnB,GAAG,EAAE,GAAG,CAAC,WAAW;QACpB,MAAM,EAAE,GAAG,CAAC,MAAM;QAClB,EAAE,EAAE,GAAG,CAAC,EAAE;QACV,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;QAChC,MAAM,EAAG,GAAW,CAAC,IAAI,EAAE,EAAE;KAC9B,CAAC;IAGF,IAAI,GAAG,CAAC,UAAU,KAAK,GAAG,IAAI,GAAG,CAAC,UAAU,KAAK,GAAG,EAAE,CAAC;QACrD,IAAA,yBAAgB,EACd,GAAG,GAAG,CAAC,UAAU,KAAK,GAAG,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,eAAe,UAAU,EACxE,QAAQ,EACR,YAAY,CACb,CAAC;IACJ,CAAC;IAED,IAAI,GAAG,CAAC,UAAU,KAAK,GAAG,EAAE,CAAC;QAC3B,IAAA,yBAAgB,EAAC,qBAAqB,EAAE,KAAK,EAAE,YAAY,CAAC,CAAC;IAC/D,CAAC;IAGD,IAAI,GAAG,CAAC,IAAI,KAAK,WAAW;QAAE,KAAK,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAC;IAC7D,IAAI,GAAG,CAAC,IAAI,KAAK,KAAK;QAAE,KAAK,GAAG,uBAAuB,CAAC,GAAG,CAAC,CAAC;IAC7D,IAAI,GAAG,CAAC,IAAI,KAAK,iBAAiB;QAAE,KAAK,GAAG,uBAAuB,CAAC,GAAG,CAAC,CAAC;IACzE,IAAI,GAAG,CAAC,IAAI,KAAK,mBAAmB;QAAE,KAAK,GAAG,cAAc,EAAE,CAAC;IAC/D,IAAI,GAAG,CAAC,IAAI,KAAK,mBAAmB;QAAE,KAAK,GAAG,qBAAqB,EAAE,CAAC;IAGtE,IAAI,GAAG,CAAC,IAAI,EAAE,UAAU,CAAC,MAAM,CAAC;QAAE,KAAK,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC;IAChE,IAAI,GAAG,CAAC,IAAI,KAAK,eAAe;QAAE,KAAK,GAAG,mBAAmB,CAAC,GAAG,CAAC,CAAC;IACnE,IAAI,GAAG,CAAC,IAAI,KAAK,cAAc,IAAI,GAAG,CAAC,OAAO,KAAK,SAAS,EAAE,CAAC;QAC7D,KAAK,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC;IAChC,CAAC;IAGD,IAAI,CAAC,CAAC,KAAK,YAAY,QAAQ,CAAC,EAAE,CAAC;QACjC,KAAK,GAAG,IAAI,QAAQ,CAAC,KAAK,CAAC,OAAO,IAAI,uBAAuB,EAAE,KAAK,CAAC,UAAU,IAAI,GAAG,CAAC,CAAC;IAC1F,CAAC;IAGD,IAAI,oBAAM,CAAC,aAAa,EAAE,CAAC;QACzB,YAAY,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IAChC,CAAC;SAAM,CAAC;QACN,aAAa,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;IACjC,CAAC;AACH,CAAC,CAAC;AAxDW,QAAA,YAAY,gBAwDvB;AAGK,MAAM,UAAU,GAAG,CAAC,EAAY,EAAE,EAAE;IACzC,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QACzD,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IACjC,CAAC,CAAC;AACJ,CAAC,CAAC;AAJW,QAAA,UAAU,cAIrB;AAGK,MAAM,eAAe,GAAG,CAAC,GAAY,EAAE,IAAc,EAAE,IAAkB,EAAQ,EAAE;IACxF,MAAM,GAAG,GAAG,IAAI,aAAa,CAAC,cAAc,GAAG,CAAC,WAAW,kBAAkB,CAAC,CAAC;IAC/E,IAAI,CAAC,GAAG,CAAC,CAAC;AACZ,CAAC,CAAC;AAHW,QAAA,eAAe,mBAG1B;AAEF,kBAAe,oBAAY,CAAC"}