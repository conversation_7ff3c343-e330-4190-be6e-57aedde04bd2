"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.initializeAGUIWebSocket = initializeAGUIWebSocket;
const express_1 = __importDefault(require("express"));
const ws_1 = require("ws");
const SupplementResearchAgent_1 = require("../agents/SupplementResearchAgent");
const logger_1 = require("../utils/logger");
const uuid_1 = require("uuid");
const router = express_1.default.Router();
const activeAgents = new Map();
const activeConnections = new Map();
function initializeAGUIWebSocket(server) {
    const wss = new ws_1.WebSocketServer({
        server,
        path: '/agui/ws'
    });
    wss.on('connection', (ws, _request) => {
        const connectionId = (0, uuid_1.v4)();
        activeConnections.set(connectionId, ws);
        logger_1.logger.info(`AG-UI WebSocket connection established: ${connectionId}`);
        ws.on('message', async (data) => {
            try {
                const message = JSON.parse(data.toString());
                await handleAGUIMessage(connectionId, message, ws);
            }
            catch (error) {
                logger_1.logger.error('Error handling AG-UI message:', error);
                ws.send(JSON.stringify({
                    type: 'error',
                    message: 'Invalid message format'
                }));
            }
        });
        ws.on('close', () => {
            logger_1.logger.info(`AG-UI WebSocket connection closed: ${connectionId}`);
            activeConnections.delete(connectionId);
            for (const [agentId, _agent] of activeAgents.entries()) {
                if (agentId.startsWith(connectionId)) {
                    activeAgents.delete(agentId);
                }
            }
        });
        ws.on('error', (error) => {
            logger_1.logger.error(`AG-UI WebSocket error for ${connectionId}:`, error);
        });
        ws.send(JSON.stringify({
            type: 'connection_established',
            connectionId,
            timestamp: new Date().toISOString()
        }));
    });
    return wss;
}
async function handleAGUIMessage(connectionId, message, ws) {
    const { type, payload } = message;
    switch (type) {
        case 'start_supplement_research':
            await handleSupplementResearch(connectionId, payload, ws);
            break;
        case 'natural_language_query':
            await handleNaturalLanguageQuery(connectionId, payload, ws);
            break;
        case 'get_graph_state':
            await handleGetGraphState(connectionId, payload, ws);
            break;
        case 'ping':
            ws.send(JSON.stringify({ type: 'pong', timestamp: new Date().toISOString() }));
            break;
        default:
            ws.send(JSON.stringify({
                type: 'error',
                message: `Unknown message type: ${type}`
            }));
    }
}
async function handleSupplementResearch(connectionId, payload, ws) {
    try {
        const { supplementName, researchDepth, includeInteractions } = payload;
        if (!supplementName) {
            ws.send(JSON.stringify({
                type: 'error',
                message: 'Supplement name is required'
            }));
            return;
        }
        const agentId = `${connectionId}-${(0, uuid_1.v4)()}`;
        const agent = new SupplementResearchAgent_1.SupplementResearchAgent();
        activeAgents.set(agentId, agent);
        const runId = (0, uuid_1.v4)();
        const threadId = (0, uuid_1.v4)();
        const researchInput = {
            supplementName,
            researchDepth: researchDepth || 'basic',
            includeInteractions: includeInteractions || false,
            runId,
            threadId,
            messages: [],
            tools: [],
            context: [],
            state: {},
            agentId
        };
        const subscription = agent.runSupplementResearch(researchInput).subscribe({
            next: (event) => {
                ws.send(JSON.stringify({
                    type: 'agent_event',
                    agentId,
                    event
                }));
            },
            error: (error) => {
                logger_1.logger.error(`Agent error for ${agentId}:`, error);
                ws.send(JSON.stringify({
                    type: 'agent_error',
                    agentId,
                    error: error.message
                }));
                activeAgents.delete(agentId);
            },
            complete: () => {
                logger_1.logger.info(`Agent completed for ${agentId}`);
                ws.send(JSON.stringify({
                    type: 'agent_completed',
                    agentId
                }));
                activeAgents.delete(agentId);
            }
        });
        agent.subscription = subscription;
    }
    catch (error) {
        logger_1.logger.error('Error starting supplement research:', error);
        ws.send(JSON.stringify({
            type: 'error',
            message: 'Failed to start supplement research'
        }));
    }
}
async function handleNaturalLanguageQuery(_connectionId, payload, ws) {
    try {
        const { query, context } = payload;
        if (!query) {
            ws.send(JSON.stringify({
                type: 'error',
                message: 'Query is required'
            }));
            return;
        }
        const agent = new SupplementResearchAgent_1.SupplementResearchAgent();
        const gemmaService = agent.gemmaService;
        const response = await gemmaService.processNaturalLanguageQuery(query, context);
        ws.send(JSON.stringify({
            type: 'query_response',
            query,
            response,
            timestamp: new Date().toISOString()
        }));
    }
    catch (error) {
        logger_1.logger.error('Error processing natural language query:', error);
        ws.send(JSON.stringify({
            type: 'error',
            message: 'Failed to process query'
        }));
    }
}
async function handleGetGraphState(_connectionId, payload, ws) {
    try {
        const { supplementName } = payload;
        const agent = new SupplementResearchAgent_1.SupplementResearchAgent();
        const graphService = agent.graphService;
        const graphData = await graphService.getKnowledgeGraph(supplementName);
        ws.send(JSON.stringify({
            type: 'graph_state',
            data: graphData,
            timestamp: new Date().toISOString()
        }));
    }
    catch (error) {
        logger_1.logger.error('Error getting graph state:', error);
        ws.send(JSON.stringify({
            type: 'error',
            message: 'Failed to get graph state'
        }));
    }
}
router.get('/agents', (_req, res) => {
    res.json({
        agents: [
            {
                id: 'supplement-research',
                name: 'Supplement Research Agent',
                description: 'Researches supplements and builds knowledge graphs',
                capabilities: [
                    'supplement_research',
                    'knowledge_graph_building',
                    'natural_language_queries'
                ]
            }
        ]
    });
});
router.get('/agents/:agentId/status', (req, res) => {
    const { agentId } = req.params;
    const agent = activeAgents.get(agentId);
    if (!agent) {
        return res.status(404).json({ error: 'Agent not found' });
    }
    return res.json({
        agentId,
        status: 'active',
        timestamp: new Date().toISOString()
    });
});
router.get('/health', async (_req, res) => {
    try {
        const agent = new SupplementResearchAgent_1.SupplementResearchAgent();
        const gemmaService = agent.gemmaService;
        const graphService = agent.graphService;
        const [gemmaHealth, graphHealth] = await Promise.all([
            gemmaService.healthCheck(),
            graphService.healthCheck()
        ]);
        res.json({
            status: 'healthy',
            services: {
                gemma: gemmaHealth,
                graph: graphHealth,
                websocket: true
            },
            activeConnections: activeConnections.size,
            activeAgents: activeAgents.size,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        logger_1.logger.error('AG-UI health check failed:', error);
        res.status(500).json({
            status: 'unhealthy',
            error: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.default = router;
//# sourceMappingURL=agui.js.map