"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.aiRoutes = void 0;
const express_1 = require("express");
const errorHandler_1 = require("@/middleware/errorHandler");
const express_validator_1 = require("express-validator");
const AIService_1 = require("@/services/AIService");
const router = (0, express_1.Router)();
exports.aiRoutes = router;
const aiService = new AIService_1.AIService();
const validateRequest = (req, _res, next) => {
    const errors = (0, express_validator_1.validationResult)(req);
    if (!errors.isEmpty()) {
        throw new errorHandler_1.ValidationError(`Validation failed: ${errors.array().map(e => e.msg).join(', ')}`);
    }
    next();
};
router.post('/analyze', [
    (0, express_validator_1.body)('text').notEmpty().withMessage('Text is required'),
    (0, express_validator_1.body)('analysisType').optional().isIn(['supplement', 'ingredient', 'interaction', 'study', 'general']).withMessage('Invalid analysis type'),
    (0, express_validator_1.body)('model').optional().isString().withMessage('Model must be a string'),
    (0, express_validator_1.body)('includeConfidence').optional().isBoolean().withMessage('includeConfidence must be a boolean'),
], validateRequest, (0, errorHandler_1.catchAsync)(async (req, res) => {
    const { text, analysisType = 'general', model, includeConfidence = true, } = req.body;
    const options = {
        analysisType,
        model,
        includeConfidence,
    };
    const analysis = await aiService.analyzeText(text, options);
    res.status(200).json({
        success: true,
        data: analysis,
        timestamp: new Date().toISOString(),
    });
}));
router.post('/extract', [
    (0, express_validator_1.body)('text').notEmpty().withMessage('Text is required'),
    (0, express_validator_1.body)('entityTypes').optional().isArray().withMessage('entityTypes must be an array'),
    (0, express_validator_1.body)('relationshipTypes').optional().isArray().withMessage('relationshipTypes must be an array'),
    (0, express_validator_1.body)('model').optional().isString().withMessage('Model must be a string'),
    (0, express_validator_1.body)('confidence').optional().isFloat({ min: 0, max: 1 }).withMessage('confidence must be between 0 and 1'),
], validateRequest, (0, errorHandler_1.catchAsync)(async (req, res) => {
    const { text, entityTypes, relationshipTypes, model, confidence = 0.7, } = req.body;
    const options = {
        entityTypes,
        relationshipTypes,
        model,
        confidence,
    };
    const extraction = await aiService.extractEntitiesAndRelationships(text, options);
    res.status(200).json({
        success: true,
        data: extraction,
        timestamp: new Date().toISOString(),
    });
}));
router.post('/recommend', [
    (0, express_validator_1.body)('conditions').optional().isArray().withMessage('conditions must be an array'),
    (0, express_validator_1.body)('goals').optional().isArray().withMessage('goals must be an array'),
    (0, express_validator_1.body)('currentSupplements').optional().isArray().withMessage('currentSupplements must be an array'),
    (0, express_validator_1.body)('restrictions').optional().isArray().withMessage('restrictions must be an array'),
    (0, express_validator_1.body)('maxRecommendations').optional().isInt({ min: 1, max: 20 }).withMessage('maxRecommendations must be between 1 and 20'),
], validateRequest, (0, errorHandler_1.catchAsync)(async (req, res) => {
    const { conditions = [], goals = [], currentSupplements = [], restrictions = [], maxRecommendations = 5, } = req.body;
    const options = {
        conditions,
        goals,
        currentSupplements,
        restrictions,
        maxRecommendations,
    };
    const recommendations = await aiService.generateRecommendations(options);
    res.status(200).json({
        success: true,
        data: recommendations,
        timestamp: new Date().toISOString(),
    });
}));
router.post('/interactions', [
    (0, express_validator_1.body)('supplements').isArray().withMessage('supplements must be an array'),
    (0, express_validator_1.body)('supplements').isLength({ min: 2 }).withMessage('At least 2 supplements are required'),
    (0, express_validator_1.body)('includeFood').optional().isBoolean().withMessage('includeFood must be a boolean'),
    (0, express_validator_1.body)('includeMedications').optional().isBoolean().withMessage('includeMedications must be a boolean'),
], validateRequest, (0, errorHandler_1.catchAsync)(async (req, res) => {
    const { supplements, includeFood = false, includeMedications = true, } = req.body;
    const options = {
        includeFood,
        includeMedications,
    };
    const interactions = await aiService.checkInteractions(supplements, options);
    res.status(200).json({
        success: true,
        data: interactions,
        timestamp: new Date().toISOString(),
    });
}));
router.post('/summarize', [
    (0, express_validator_1.body)('text').notEmpty().withMessage('Text is required'),
    (0, express_validator_1.body)('summaryType').optional().isIn(['brief', 'detailed', 'technical', 'consumer']).withMessage('Invalid summary type'),
    (0, express_validator_1.body)('maxLength').optional().isInt({ min: 50, max: 2000 }).withMessage('maxLength must be between 50 and 2000'),
    (0, express_validator_1.body)('focusAreas').optional().isArray().withMessage('focusAreas must be an array'),
], validateRequest, (0, errorHandler_1.catchAsync)(async (req, res) => {
    const { text, summaryType = 'brief', maxLength = 500, focusAreas = [], } = req.body;
    const options = {
        summaryType,
        maxLength,
        focusAreas,
    };
    const summary = await aiService.summarizeText(text, options);
    res.status(200).json({
        success: true,
        data: summary,
        timestamp: new Date().toISOString(),
    });
}));
router.post('/questions', [
    (0, express_validator_1.body)('topic').notEmpty().withMessage('Topic is required'),
    (0, express_validator_1.body)('questionType').optional().isIn(['research', 'safety', 'efficacy', 'dosage', 'general']).withMessage('Invalid question type'),
    (0, express_validator_1.body)('count').optional().isInt({ min: 1, max: 20 }).withMessage('count must be between 1 and 20'),
    (0, express_validator_1.body)('difficulty').optional().isIn(['basic', 'intermediate', 'advanced']).withMessage('Invalid difficulty level'),
], validateRequest, (0, errorHandler_1.catchAsync)(async (req, res) => {
    const { topic, questionType = 'general', count = 5, difficulty = 'intermediate', } = req.body;
    const options = {
        questionType,
        count,
        difficulty,
    };
    const questions = await aiService.generateQuestions(topic, options);
    res.status(200).json({
        success: true,
        data: questions,
        timestamp: new Date().toISOString(),
    });
}));
router.post('/validate', [
    (0, express_validator_1.body)('claims').isArray().withMessage('claims must be an array'),
    (0, express_validator_1.body)('supplement').notEmpty().withMessage('supplement is required'),
    (0, express_validator_1.body)('evidenceLevel').optional().isIn(['low', 'medium', 'high']).withMessage('Invalid evidence level'),
], validateRequest, (0, errorHandler_1.catchAsync)(async (req, res) => {
    const { claims, supplement, evidenceLevel = 'medium', } = req.body;
    const options = {
        evidenceLevel,
    };
    const validation = await aiService.validateClaims(claims, supplement, options);
    res.status(200).json({
        success: true,
        data: validation,
        timestamp: new Date().toISOString(),
    });
}));
router.get('/models', (0, errorHandler_1.catchAsync)(async (_req, res) => {
    const models = await aiService.getAvailableModels();
    res.status(200).json({
        success: true,
        data: models,
        timestamp: new Date().toISOString(),
    });
}));
router.get('/stats', (0, errorHandler_1.catchAsync)(async (_req, res) => {
    const stats = await aiService.getStats();
    res.status(200).json({
        success: true,
        data: stats,
        timestamp: new Date().toISOString(),
    });
}));
router.post('/chat', [
    (0, express_validator_1.body)('message').notEmpty().withMessage('Message is required'),
    (0, express_validator_1.body)('context').optional().isArray().withMessage('Context must be an array'),
    (0, express_validator_1.body)('model').optional().isString().withMessage('Model must be a string'),
    (0, express_validator_1.body)('temperature').optional().isFloat({ min: 0, max: 2 }).withMessage('Temperature must be between 0 and 2'),
], validateRequest, (0, errorHandler_1.catchAsync)(async (req, res) => {
    const { message, context = [], model, temperature, } = req.body;
    const options = {
        context,
        model,
        temperature,
    };
    const response = await aiService.chat(message, options);
    res.status(200).json({
        success: true,
        data: response,
        timestamp: new Date().toISOString(),
    });
}));
//# sourceMappingURL=ai.js.map